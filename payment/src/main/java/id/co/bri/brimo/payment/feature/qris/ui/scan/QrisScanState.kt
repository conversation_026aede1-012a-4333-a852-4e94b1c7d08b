package id.co.bri.brimo.payment.feature.qris.ui.scan

import id.co.bri.brimo.payment.app.QrisScanRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class QrisScanState(
    val qrisScanRoute: QrisScanRoute,
    val qrisScan: SharedFlow<UiState<QrisModel>> = MutableSharedFlow(),
    val qrTapPayload: SharedFlow<UiState<QrTapPayloadResponse>> = MutableSharedFlow()
)

internal sealed class QrisScanEvent {
    data class ScanQr(val stringQr: String) : QrisScanEvent()
    data class PayloadQr(val pin: String) : QrisScanEvent()
}

internal sealed class QrisScanNavigation {
    object Back : QrisScanNavigation()
    object QrShow : QrisScanNavigation()
    object QrTransfer : QrisScanNavigation()
    data class QrTap(val qrisData: String) : QrisScanNavigation()
    data class Nominal(
        val qrisData: String,
        val type: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()

    data class Confirmation(
        val qrisData: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()

    data class Park(
        val qrisData: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()
}
