package id.co.bri.brimo.payment.feature.qrtap.data.impl

import id.co.bri.brimo.payment.core.network.processApi
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

internal class QrTapRepositoryImpl(
    private val ioDispatcher: CoroutineDispatcher
) : QrTapRepository {

    override suspend fun postQrTapPayload(
        request: QrTapPayloadRequest,
        fastMenu: Boolean
    ): QrTapPayloadResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "7n2KY5arQKdEIwmxNVukJUflTboZSgOtd+pQ5kiHYaI="
            } else {
                "NLTwcRrqV1oG8XwCFWZF8w=="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
                delay(1000)
                """
                    {
                      "code": "00",
                      "description": "Sukses",
                      "data": {
                          "account_no": "**********",
                          "card_no": "****************",
                          "card_holder": "John Doe",
                          "nfc_type": "TypeA",
                          "nfc_type_image": "https://example.com/nfc-image.png",
                          "payload": "someEncryptedPayloadData",
                          "active_for": 300,
                          "card_token": "abc123securetoken",
                          "tap_guidance": "Hold your card near the reader",
                          "is_cc": true,
                          "account_list": [],
                          "credit_card_list": []
                        }
                    }
                """.trimIndent()
            }
        }

    override suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): SaldoNormalResponse =
        withContext(ioDispatcher) {
            val url = "gnUBRK3Mc0ogiZdhmA6hBg=="
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }
}
