package id.co.bri.brimo.payment.feature.transfer.ui.confirmation

import androidx.activity.ComponentActivity
import androidx.activity.addCallback
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.launchAndCollectIn
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.TopBar
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_0047CC
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_27AE60
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.DataException
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.request.TransferPayRequest
import id.co.bri.brimo.payment.core.network.response.TransferConfirmationData
import id.co.bri.brimo.payment.core.network.response.TransferPayResponse
import id.co.bri.brimo.payment.feature.briva.ui.confirmation.BrivaConfirmationEvent
import id.co.bri.brimo.payment.feature.briva.ui.confirmation.BrivaConfirmationNavigation
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.brizzi.ui.process.ProcessDialog
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationNavigation.Back
import id.co.bri.brimo.payment.feature.transfer.ui.input.nominal.TransferInputNominalViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun TransferConfirmationScreen(
    activity: ComponentActivity,
    navigation: (TransferConfirmationNavigation) -> Unit = {},
    transferConfirmationViewModel: TransferConfirmationViewModel = koinViewModel(),
    transferInputNominalViewModel: TransferInputNominalViewModel = koinViewModel(),
    isRtgs: Boolean,
    isFastMenu: Boolean,
    isFav: Boolean
) {
    val confirmationState by transferInputNominalViewModel.confirmationData.collectAsStateWithLifecycle()
    val confirmationRtgsState by transferInputNominalViewModel.confirmationRtgsData.collectAsStateWithLifecycle()
    val paymentState = transferConfirmationViewModel.transferPayment
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }
    val lifecycleOwner = LocalLifecycleOwner.current

    BackHandler {
        warningBottomSheet = true
    }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = "Yakin ingin kembali?",
        description = "Kamu akan keluar dari halaman konfirmasi. Transaksi belum diproses.",
        primaryText = "Lanjutkan Transaksi",
        secondaryText = "Kembali",
        onSecondary = {
            navigation(TransferConfirmationNavigation.Back)
            transferInputNominalViewModel.clearConfirmationData()
        }
    )

    when {
        confirmationState is UiState.Success -> {
            val confirmationData =
                (confirmationState as UiState.Success<TransferConfirmationData>).data
            ConfirmationContent(
                activity = activity,
                event = transferConfirmationViewModel::handleEvent,
                navigation = navigation,
                dataConfirmation = confirmationData,
                paymentState = paymentState,
                onBack = {
                    warningBottomSheet = true
                },
                isRtgs = isRtgs,
                isFastMenu = isFastMenu,
                isFav = isFav
            )
        }

        confirmationRtgsState is UiState.Success -> {
            val confirmationData =
                (confirmationRtgsState as UiState.Success<TransferConfirmationData>).data
            ConfirmationContent(
                activity = activity,
                event = transferConfirmationViewModel::handleEvent,
                dataConfirmation = confirmationData,
                paymentState = paymentState,
                navigation = navigation,
                onBack = {
                    warningBottomSheet = true
                },
                isRtgs = isRtgs,
                isFastMenu = isFastMenu,
                isFav = isFav
            )
        }

        confirmationState is UiState.Loading || confirmationRtgsState is UiState.Loading -> {
            ConfirmationSkeletonContent()
        }

        confirmationState is UiState.Error || confirmationRtgsState is UiState.Error -> {
            ConfirmationSkeletonContent()
            ErrorBottomSheet(
                showBottomSheet = true,
                isRetry = false,
                onClose = {
                    navigation(TransferConfirmationNavigation.BackToTransferForm)
                },
                onDismiss = {
                    navigation(TransferConfirmationNavigation.BackToTransferForm)
                }
            )
        }

        else -> {
        }
    }
}

@Composable
private fun ConfirmationSkeletonContent() {
    val infiniteTransition = rememberInfiniteTransition(label = "skeleton")
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.2f,
        targetValue = 0.4f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Box(
                    modifier = Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(8.dp))
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = painterResource(R.drawable.form_background),
                    sizeToIntrinsics = false,
                    contentScale = ContentScale.Crop
                )
                .padding(innerPadding)
        ) {
            TopBar(
                title = "Konfirmasi",
                onBack = { }
            )

            Spacer(modifier = Modifier.height(24.dp))

            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier
                        .width(105.dp)
                        .padding(horizontal = 16.dp)
                        .height(20.dp)
                        .background(Color_0047CC.copy(alpha = alpha), RoundedCornerShape(16.dp))
                )

                Spacer(modifier = Modifier.height(8.dp))

                Box(
                    modifier = Modifier
                        .width(135.dp)
                        .padding(horizontal = 16.dp)
                        .height(32.dp)
                        .background(Color_0047CC.copy(alpha = alpha), RoundedCornerShape(16.dp))
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
                    .padding(horizontal = 16.dp)
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // Transaction Purpose skeleton
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(24.dp)
                        .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(4.dp))
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Transaction cards skeleton
                repeat(2) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp)
                            .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(16.dp))
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Save to favorite skeleton
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(16.dp))
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Bill details skeleton
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(24.dp)
                        .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(4.dp))
                )

                Spacer(modifier = Modifier.height(16.dp))

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                        .background(Color_F5F7FB.copy(alpha = alpha), RoundedCornerShape(16.dp))
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ConfirmationContent(
    activity: ComponentActivity,
    event: (TransferConfirmationEvent) -> Unit = {},
    dataConfirmation: TransferConfirmationData,
    paymentState: StateFlow<UiState<TransferPayResponse>>,
    navigation: (TransferConfirmationNavigation) -> Unit = {},
    onBack: () -> Unit,
    isRtgs: Boolean = false,
    isFastMenu: Boolean,
    isFav: Boolean
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var processDialog by rememberSaveable { mutableStateOf(false) }
    var nameField by rememberSaveable { mutableStateOf("") }
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    LaunchedEffect(Unit) {
        paymentState.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess { data ->
                    progressDialog = false
                    if (false) return@onSuccess

                    val transferPayment = data.serialize().orEmpty()
                    if (transferPayment.isEmpty()) {
                        errorBottomSheet = true
                    } else {
                        scope.launch {
                            processDialog = true
                            delay(2500)
                            navigation(
                                TransferConfirmationNavigation.Payment(
                                    transferPayment
                                )
                            )
                        }
                    }
                }
                .onError { e ->
                    progressDialog = false
                    deletePin()
                    selectedPin = ""
                    if (e is MessageException && e.errorSnackbar()) {
                        if (e.description.contains("pin", true)) {
                            pinMessage = e.description
                        } else {
                            scope.launch {
                                snackbarType = SnackbarType.ERROR
                                snackbarHostState.showSnackbar(e.description)
                            }
                        }
                    } else {
                        error = e
                        errorBottomSheet = true
                    }
                }
        }
    }

    if (progressDialog) ProgressDialog()

    if (processDialog) {
        Dialog(
            onDismissRequest = { processDialog = false },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                ProcessDialog()
            }
        }
    }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onDismiss = {
            if (error is MessageException || error is DataException) {
                navigation(TransferConfirmationNavigation.BackToTransferForm)
            }
        },
        onClose = {
            if (error is MessageException || error is DataException) {
                navigation(TransferConfirmationNavigation.BackToTransferForm)
            }
        },
        onRetry = {
            event(
                if (isRtgs) {
                    TransferConfirmationEvent.PaymentRtgs(
                        isFastMenu =  isFastMenu,
                        TransferPayRequest(
                            pfmCategory = dataConfirmation.pfmCategory.toString(),
                            pin = selectedPin,
                            referenceNumber = dataConfirmation.referenceNumber.toString(),
                            saveAs = nameField
                        )
                    )
                } else {
                    TransferConfirmationEvent.Payment(
                        isFastMenu =  isFastMenu,
                        TransferPayRequest(
                            pfmCategory = dataConfirmation.pfmCategory.toString(),
                            pin = selectedPin,
                            referenceNumber = dataConfirmation.referenceNumber.toString(),
                            saveAs = nameField
                        )
                    )
                }
            )
        },
        isRetry = !(error is MessageException || error is DataException)
    )

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        event(
                            if (isRtgs) {
                                TransferConfirmationEvent.PaymentRtgs(
                                    isFastMenu =  isFastMenu,
                                    TransferPayRequest(
                                        pfmCategory = dataConfirmation.pfmCategory.toString(),
                                        pin = pin,
                                        referenceNumber = dataConfirmation.referenceNumber.toString(),
                                        saveAs = nameField
                                    )
                                )
                            } else {
                                TransferConfirmationEvent.Payment(
                                    isFastMenu =  isFastMenu,
                                    TransferPayRequest(
                                        pfmCategory = dataConfirmation.pfmCategory.toString(),
                                        pin = pin,
                                        referenceNumber = dataConfirmation.referenceNumber.toString(),
                                        saveAs = nameField
                                    )
                                )
                            }
                        )
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Button(
                    onClick = {
                        pinDialog = true
                    },
                    modifier = Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .height(56.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color_0054F3,
                        contentColor = Color.White
                    )
                ) {
                    Text(
                        text = "Bayar Sekarang",
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Text(
                        text = dataConfirmation.payAmountString,
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                TopBar(
                    title = "Konfirmasi",
                    onBack = onBack
                )

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Total Transfer",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge
                )

                Text(
                    text = dataConfirmation.payAmountString,
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.headlineLarge
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Tujuan Transaksi",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            AsyncImage(
                                model = dataConfirmation.sourceAccountDataView.iconPath,
                                contentDescription = null,
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape),
                                placeholder = painterResource(id = R.drawable.thumbnail),
                                error = painterResource(id = R.drawable.thumbnail),
                                contentScale = ContentScale.Crop
                            )

                            Spacer(modifier = Modifier.width(12.dp))

                            Column(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    text = dataConfirmation.sourceAccountDataView.title,
                                    modifier = Modifier.fillMaxWidth(),
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.height(2.dp))

                                Text(
                                    text = "${
                                        dataConfirmation.sourceAccountDataView.subtitle.substringAfter(
                                            " "
                                        )
                                    } " + "- ${dataConfirmation.sourceAccountDataView.description}",
                                    modifier = Modifier.fillMaxWidth(),
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(R.drawable.receipt_icon_payment),
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                                    .size(16.dp),
                                contentScale = ContentScale.Fit
                            )

                            Spacer(modifier = Modifier.width(12.dp))

                            DividerHorizontal()
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            AsyncImage(
                                model = dataConfirmation.billingDetail.iconPath,
                                contentDescription = null,
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape),
                                placeholder = painterResource(id = R.drawable.thumbnail),
                                error = painterResource(id = R.drawable.thumbnail),
                                contentScale = ContentScale.Crop
                            )

                            Spacer(modifier = Modifier.width(12.dp))

                            Column(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    text = dataConfirmation.billingDetail.title,
                                    modifier = Modifier.fillMaxWidth(),
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.height(2.dp))

                                Text(
                                    text = "${
                                        dataConfirmation.billingDetail.subtitle.substringAfter(
                                            " "
                                        )
                                    } " +
                                            "- ${dataConfirmation.billingDetail.description}",
                                    modifier = Modifier.fillMaxWidth(),
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    if (!isFav && !isFastMenu) {
                        Spacer(modifier = Modifier.height(16.dp))

                        var switch by rememberSaveable { mutableStateOf(false) }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Simpan ke Favorit",
                                modifier = Modifier.weight(1f),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Switch(
                                checked = switch,
                                onCheckedChange = {
                                    switch = it
                                },
                                modifier = Modifier
                                    .height(24.dp)
                                    .scale(0.75f),
                                colors = SwitchDefaults.colors(
                                    checkedTrackColor = Color_27AE60,
                                    uncheckedTrackColor = Color_E9EEF6,
                                    checkedBorderColor = Color.Transparent,
                                    uncheckedBorderColor = Color.Transparent,
                                    checkedThumbColor = Color.White,
                                    uncheckedThumbColor = Color.White
                                )
                            )
                        }

                        if (switch) {
                            Spacer(modifier = Modifier.height(16.dp))

                            TextFieldCustom(
                                value = nameField,
                                onValueChange = { nameField = it },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(64.dp),
                                textStyle = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
                                label = {
                                    Text(text = "Nama Tersimpan")
                                },
                                trailingIcon = if (nameField.isNotEmpty()) {
                                    {
                                        Image(
                                            painter = painterResource(R.drawable.icon_close),
                                            contentDescription = null,
                                            modifier = Modifier
                                                .size(16.dp)
                                                .clickable {
                                                    nameField = ""
                                                },
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                } else {
                                    null
                                },
                                singleLine = true,
                                shape = RoundedCornerShape(16.dp),
                                colors = textFieldColors(),
                                contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                            )
                        }
                    }


                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Detail Transaksi",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                    ) {
                        dataConfirmation.detailDataView?.forEachIndexed { index, item ->
                            Row(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    text = item.name.orEmpty(),
                                    modifier = Modifier.weight(1f),
                                    color = Color_7B90A6,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = item.value.orEmpty(),
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.End,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }

                            if (index < dataConfirmation.detailDataView.size - 1) {
                                Spacer(modifier = Modifier.height(12.dp))
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun PreviewBrizziConfirmation() {
    MainTheme {
        ConfirmationSkeletonContent()
    }
}
