package id.co.bri.brimo.payment.feature.qrtap.data.api

import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse

internal interface QrTapRepository {

    suspend fun postQrTapPayload(
        request: QrTapPayloadRequest,
        fastMenu: Boolean
    ): QrTapPayloadResponse

    suspend fun postSaldoNormal(request: SaldoNormalRequest): SaldoNormalResponse
}
