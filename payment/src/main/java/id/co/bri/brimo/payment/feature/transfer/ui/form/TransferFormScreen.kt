package id.co.bri.brimo.payment.feature.transfer.ui.form

import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.splitBySpacePer4Char
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.EmptyState
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.Shimmer
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TopBar
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.component.dashedBorder
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E6EEFF
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.request.FavoriteRequest
import id.co.bri.brimo.payment.core.network.response.AftBanner
import id.co.bri.brimo.payment.core.network.response.BankItem
import id.co.bri.brimo.payment.core.network.response.TransferFormData
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.base.HistoryResponse
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaFavoriteSection
import id.co.bri.brimo.payment.feature.briva.ui.base.BrivaHistorySection
import id.co.bri.brimo.payment.feature.brizzi.ui.favorite.FavoriteBottomSheet
import id.co.bri.brimo.payment.feature.transfer.ui.component.BankListBottomSheet
import id.co.bri.brimo.payment.feature.transfer.ui.component.TransferSearchDialog
import id.co.bri.brimo.payment.feature.transfer.ui.form.TransferFormNavigation.Back
import id.co.bri.brimo.payment.feature.transfer.ui.input.accountnumber.InputAccountNumberViewModel
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun TransferFormScreen(
    fastMenu: Boolean,
    onBankSelected: (bankCode: String, bankName: String, imageUrl: String) -> Unit,
    navigation: (TransferFormNavigation) -> Unit = {},
    transferFormViewModel: TransferFormViewModel = koinViewModel(),
    inputAccountNumberViewModel: InputAccountNumberViewModel = koinViewModel()
) {
    LaunchedEffect(Unit) {
        transferFormViewModel.postTransferFormInit(fastMenu)
    }
    val transferFormState by transferFormViewModel.transferFormInitData.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }
    val scope = rememberCoroutineScope()
    val addFavoriteState by transferFormViewModel.addFavoriteData.collectAsStateWithLifecycle()
    val updateFavoriteState by transferFormViewModel.updateFavoriteData.collectAsStateWithLifecycle()

    LaunchedEffect(addFavoriteState) {
        addFavoriteState
            ?.onSuccess { data ->
                scope.launch {
                    snackbarType = SnackbarType.SUCCESS
                    snackbarHostState.showSnackbar("Data Favorit berhasil ditambahkan")
                }
                transferFormViewModel.clearFavoriteState()
            }
    }

    LaunchedEffect(updateFavoriteState) {
        updateFavoriteState
            ?.onSuccess { data ->
                scope.launch {
                    snackbarType = SnackbarType.SUCCESS
                    snackbarHostState.showSnackbar("Nama favorit berhasil diubah")
                }
                transferFormViewModel.clearFavoriteState()
            }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        SnackbarCustom(
            modifier = Modifier
                .padding(top = 24.dp)
                .padding(16.dp),
            hostState = snackbarHostState,
            type = snackbarType
        )

        transferFormState
            ?.onLoading {
                TransferFormContentLoading(
                    fastMenu = fastMenu,
                    onBankSelected = onBankSelected,
                    navigation = navigation,
                    event = transferFormViewModel::handleEvent,
                    viewModel = transferFormViewModel,
                    inputAccountNumberViewModel = inputAccountNumberViewModel
                )
            }
            ?.onSuccess { data ->
                TransferFormContent(
                    fastMenu = fastMenu,
                    onBankSelected = onBankSelected,
                    transferForm = data,
                    navigation = navigation,
                    event = transferFormViewModel::handleEvent,
                    viewModel = transferFormViewModel,
                    inputAccountNumberViewModel = inputAccountNumberViewModel
                )
            }
            ?.onError { e ->
                ErrorBottomSheet(
                    error = e,
                    onRetry = {
                        transferFormViewModel.handleEvent(
                            TransferFormEvent.RefreshTransferForm
                        )
                    },
                    onDismiss = {
                        navigation(Back)
                    },
                    onClose = {
                        navigation(Back)
                    }
                )
            }
    }
}

@Composable
private fun TransferFormContent(
    fastMenu: Boolean,
    onBankSelected: (String, String, String) -> Unit,
    transferForm: TransferFormData,
    navigation: (TransferFormNavigation) -> Unit,
    event: (TransferFormEvent) -> Unit = {},
    viewModel: TransferFormViewModel,
    inputAccountNumberViewModel: InputAccountNumberViewModel,
) {
    var bankListBottomSheet by rememberSaveable { mutableStateOf(false) }
    var searchField by rememberSaveable { mutableStateOf("") }
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    var selectedFavorite: FavoriteResponse? by remember { mutableStateOf(null) }
    var favoriteBottomSheet by rememberSaveable { mutableStateOf(false) }
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    var selectedBankImageUrl by rememberSaveable { mutableStateOf("") }
    var favoriteData: List<FavoriteResponse>? by remember {
        mutableStateOf(transferForm.saved)
    }
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }
    var favName by rememberSaveable { mutableStateOf("") }

    val transferFormState by viewModel.favoriteData.collectAsStateWithLifecycle()
    val unpinFavoriteState by viewModel.unpinFavoriteData.collectAsStateWithLifecycle()
    val addFavoriteState by viewModel.addFavoriteData.collectAsStateWithLifecycle()
    val pinFavoriteState by viewModel.pinFavoriteData.collectAsStateWithLifecycle()
    val deleteFavoriteState by viewModel.deleteFavoriteData.collectAsStateWithLifecycle()
    val transferInquiryState by inputAccountNumberViewModel.transferInquiryData.collectAsStateWithLifecycle()
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }
    var openSearchDialog by rememberSaveable { mutableStateOf(false) }
    var productId by rememberSaveable { mutableStateOf("") }
    var savedId by rememberSaveable { mutableStateOf("") }

    transferFormState
        ?.onSuccess { data ->
            favoriteData = data.saved
//            progressDialog = false
        }
        ?.onLoading {
//            ProgressDialog()
        }
        ?.onError { e ->
//            progressDialog = false
            if (e is MessageException) {
                scope.launch {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                }
            } else {
                errorBottomSheet = true
            }
        }

    LaunchedEffect(transferInquiryState) {
        transferInquiryState
            ?.onSuccess { data ->
                progressDialog = false
                navigation(TransferFormNavigation.ToInputNominal(selectedBankImageUrl, favName))
            }
            ?.onLoading {
                progressDialog = true
            }
            ?.onError { e ->
                progressDialog = false
                if (e is MessageException) {
                    scope.launch {
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                } else {
                    errorBottomSheet = true
                }
                inputAccountNumberViewModel.clearTransferInquiry()
            }
    }

    LaunchedEffect(addFavoriteState) {
        addFavoriteState
            ?.onSuccess { data ->
                scope.launch {
                    snackbarType = SnackbarType.SUCCESS
                    snackbarHostState.showSnackbar("Data Favorit berhasil ditambahkan")
                }
                viewModel.clearFavoriteState()
            }
    }

    if (unpinFavoriteState is UiState.Success) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.SUCCESS
                snackbarHostState.showSnackbar("Berhasil hapus pin favorit transfer.")
            }
            viewModel.postTransferForm(fastMenu)
            viewModel.clearFavoriteState()
        }
    } else if (unpinFavoriteState is UiState.Loading) {
        progressDialog = true
    } else if (unpinFavoriteState is UiState.Error) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.ERROR
                snackbarHostState.showSnackbar("Gagal hapus pin favorit transfer.")
            }
        }
    }

    if (pinFavoriteState is UiState.Success) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.SUCCESS
                snackbarHostState.showSnackbar("Berhasil pin favorit transfer.")
            }
            viewModel.postTransferForm(fastMenu)
            viewModel.clearFavoriteState()
        }
    } else if (pinFavoriteState is UiState.Loading) {
        progressDialog = true
    } else if (pinFavoriteState is UiState.Error) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.ERROR
                snackbarHostState.showSnackbar("Gagal pin favorit transfer.")
            }
        }
    }

    if (deleteFavoriteState is UiState.Success) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.SUCCESS
                snackbarHostState.showSnackbar("Daftar favorit berhasil dihapus")
            }
            viewModel.postTransferForm(fastMenu)
            viewModel.clearFavoriteState()
        }
    } else if (deleteFavoriteState is UiState.Loading) {
        progressDialog = true
    } else if (deleteFavoriteState is UiState.Error) {
        progressDialog = false
        LaunchedEffect(Unit) {
            scope.launch {
                snackbarType = SnackbarType.ERROR
                snackbarHostState.showSnackbar("Gagal hapus daftar favorit.")
            }
            viewModel.postTransferForm(fastMenu)
            viewModel.clearFavoriteState()
        }
    }

    if (progressDialog) ProgressDialog()

    if (openSearchDialog) {
        Dialog(
            onDismissRequest = { openSearchDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                favoriteData?.let {
                    transferForm.history?.let { history ->
                        TransferSearchDialog(
                            favorite = it,
                            history = history,
                            isFastMenu = fastMenu,
                            onOptionFavorite = { item ->
                                selectedFavorite = item
                                favoriteBottomSheet = true
                            },
                            onSelectFavorite = { item ->
                                val bank = item.value?.split("|")
                                openSearchDialog = false
                                inputAccountNumberViewModel.postTransferInquiry(
                                    isFastMenu = fastMenu,
                                    accountNumber = bank?.get(2) ?: "",
                                    bankCode = bank?.get(1) ?: ""
                                )
                            },
                            onSelectHistory = { item ->
                                val bank = item.value?.split("|")
                                openSearchDialog = false
                                inputAccountNumberViewModel.postTransferInquiry(
                                    isFastMenu = fastMenu,
                                    accountNumber = bank?.get(1) ?: "",
                                    bankCode = bank?.get(0) ?: ""
                                )
                            },
                            onBack = {
                                openSearchDialog = false
                            }
                        )
                    }
                }
            }
        }
    }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            with(inputAccountNumberViewModel) {
                postTransferInquiry(
                    isFastMenu = fastMenu,
                    accountNumber = lastAccountNumber,
                    bankCode = lastBankCode
                )
            }
        }
    )

    BottomSheet(
        showBottomSheet = bankListBottomSheet,
        onShowBottomSheet = { bankListBottomSheet = it }
    ) { dismiss ->
        BankListBottomSheet(
            data = transferForm.bankList,
            onSelect = { bank ->
                onBankSelected(bank.code, bank.name, bank.imageUrl)
                selectedBankImageUrl = bank.imageUrl
                bankListBottomSheet = false
            },
            onClose = {
                bankListBottomSheet = false
            }
        )
    }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = "Hapus Data Favorit?",
        description = "Apakah kamu yakin ingin menghapus data ini dari daftar favorit transfer?",
        primaryText = "Batalkan",
        secondaryText = "Hapus",
        onSecondary = {
            event(
                TransferFormEvent.DeleteFavorite(
                    FavoriteRequest(
                        method = "",
                        productId = productId,
                        savedId = savedId
                    )
                )
            )
        }
    )

    BottomSheet(
        showBottomSheet = favoriteBottomSheet,
        onShowBottomSheet = { favoriteBottomSheet = it }
    ) { dismiss ->
        val valueSplit = selectedFavorite?.value?.split("|")
        productId = valueSplit?.get(1) ?: ""
        savedId = valueSplit?.get(0) ?: ""
        FavoriteBottomSheet(
            isPin = selectedFavorite?.favorite == true,
            onPin = {
                dismiss {
                    event(
                        if (selectedFavorite?.favorite == true) {
                            TransferFormEvent.UnpinFavorite(
                                FavoriteRequest(
                                    method = "",
                                    productId = productId,
                                    savedId = savedId
                                )
                            )
                        } else {
                            TransferFormEvent.PinFavorite(
                                FavoriteRequest(
                                    method = "",
                                    productId = productId,
                                    savedId = savedId

                                )
                            )
                        }
                    )
                }
            },
            onEditFavorite = {
                dismiss {
                    viewModel.setSelectedFavorite(selectedFavorite)
                    navigation(TransferFormNavigation.EditFavorite(false))
                }
            },
            onDeleteFavorite = {
                dismiss {
                    warningBottomSheet = true
                }
            },
            onClose = {
                dismiss {}
            }
        )
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            if (!fastMenu) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                ) {

                    DividerHorizontal()

                    Button(
                        onClick = {
                            bankListBottomSheet = true
                        },
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color_0054F3,
                            contentColor = Color.White
                        )
                    ) {
                        Text(
                            text = "Buat Transaksi Baru",
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyLarge,
                        )
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                TopBar(title = "Transfer", onBack = {
                    navigation(Back)
                })

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp, vertical = 24.dp)
                ) {
                    if (!fastMenu) {
                        val tabs1 = listOf("Dalam Negeri")
                        var page1 by rememberSaveable { mutableIntStateOf(0) }


                        Row {
                            tabs1.forEachIndexed { index, data ->
                                val selected = page1 == index
                                val fontWeight =
                                    if (selected) FontWeight.SemiBold else FontWeight.Normal
                                val color = if (selected) Color_0054F3 else Color_7B90A6

                                Column(
                                    modifier = Modifier
                                        .width(IntrinsicSize.Max)
                                        .clickable { page1 = index }
                                ) {
                                    Text(
                                        text = data,
                                        color = color,
                                        fontWeight = fontWeight,
                                        style = MaterialTheme.typography.bodyLarge
                                    )

                                    Spacer(modifier = Modifier.height(12.dp))

                                    if (selected) {
                                        DividerHorizontal(
                                            thickness = 3.dp,
                                            color = Color_0054F3
                                        )
                                    }
                                }

                                Spacer(modifier = Modifier.width(16.dp))
                            }
                        }

                        DividerHorizontal()

                        Spacer(modifier = Modifier.height(24.dp))
                    }


                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(24.dp))
                            .clickable {
                                openSearchDialog = true
                            }
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "Cari nama atau no. rekening",
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    val tabs2 = listOf("Favorit", "Riwayat")
                    var page2 by rememberSaveable { mutableIntStateOf(0) }

                    Row {
                        tabs2.forEachIndexed { index, tab ->
                            val selected = page2 == index
                            val backgroundColor = if (selected) Color_E6EEFF else Color_F5F7FB
                            val textColor = if (selected) Color_0054F3 else Color.Black

                            Text(
                                text = tab,
                                modifier = Modifier
                                    .background(backgroundColor, RoundedCornerShape(100))
                                    .clickable {
                                        page2 = index
                                    }
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                color = textColor,
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.width(12.dp))
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    when (page2) {
                        0 -> {
                            if (favoriteData.isNullOrEmpty()) {
                                Box(modifier = Modifier.fillMaxSize()) {
                                    EmptyState(
                                        modifier = Modifier.align(Alignment.Center),
                                        title = "Belum Ada Daftar Favorit",
                                        description = "Yuk, tambah favorit biar transaksi berikutnya lebih cepat.",
                                        addButton = !fastMenu,
                                        onClick = {
                                            navigation(
                                                TransferFormNavigation.AddFavorite(
                                                    bankCode = transferForm.bankList[0].code,
                                                    bankName = transferForm.bankList[0].name,
                                                    imageUrl = transferForm.bankList[0].imageUrl,
                                                    isAddFavorite = true
                                                )
                                            )
                                        }
                                    )
                                }
                            } else {
                                if (!fastMenu) {
                                    Row(
                                        modifier = Modifier
                                            .dashedBorder()
                                            .padding(horizontal = 16.dp, vertical = 16.dp)
                                            .clickable {
                                                navigation(
                                                    TransferFormNavigation.AddFavorite(
                                                        bankCode = transferForm.bankList[0].code,
                                                        bankName = transferForm.bankList[0].name,
                                                        imageUrl = transferForm.bankList[0].imageUrl,
                                                        isAddFavorite = true
                                                    )
                                                )
                                            },
                                        verticalAlignment = Alignment.CenterVertically

                                    ) {
                                        Image(
                                            painter = painterResource(R.drawable.icon_favorite),
                                            contentDescription = null,
                                            modifier = Modifier.size(32.dp),
                                        )

                                        Spacer(modifier = Modifier.width(12.dp))

                                        Text(
                                            text = "Tambahkan daftar favorit",
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.weight(1f))

                                        Icon(
                                            painter = painterResource(R.drawable.icon_add_circle),
                                            contentDescription = null,
                                            modifier = Modifier.size(24.dp),
                                        )
                                    }

                                    Spacer(modifier = Modifier.height(24.dp))
                                }

                                BrivaFavoriteSection(
                                    data = favoriteData?.map {
                                        it.copy(description = it.description?.splitBySpacePer4Char())
                                    } ?: listOf(),
                                    showOption = !fastMenu,
                                    onOption = { item ->
                                        selectedFavorite = item
                                        favoriteBottomSheet = true
                                    },
                                    onSelect = { item ->
                                        val bank = item.value?.split("|")
                                        favName = item.title ?: ""
                                        inputAccountNumberViewModel.postTransferInquiry(
                                            isFastMenu = fastMenu,
                                            accountNumber = bank?.get(2) ?: "",
                                            bankCode = bank?.get(1) ?: ""
                                        )
                                    }
                                )
                            }
                        }

                        1 -> {
                            if (transferForm.history.isNullOrEmpty()) {
                                BrivaHistorySection(
                                    data = emptyList(),
                                    onSelect = {}
                                )
                            } else {
                                BrivaHistorySection(
                                    data = transferForm.history.map {
                                        it.copy(description = it.description?.splitBySpacePer4Char())
                                    },
                                    onSelect = { item ->
                                        val history =
                                            transferForm.history.find { it.description == item.description }
                                        val bank = item.value?.split("|")
                                        inputAccountNumberViewModel.postTransferInquiry(
                                            isFastMenu = fastMenu,
                                            accountNumber = bank?.get(1) ?: "",
                                            bankCode = bank?.get(0) ?: ""
                                        )
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun TransferFormContentLoading(
    fastMenu: Boolean,
    onBankSelected: (String, String, String) -> Unit,
    navigation: (TransferFormNavigation) -> Unit,
    event: (TransferFormEvent) -> Unit = {},
    viewModel: TransferFormViewModel,
    inputAccountNumberViewModel: InputAccountNumberViewModel,
) {
    val infiniteTransition = rememberInfiniteTransition(label = "skeleton")
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.2f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            if (!fastMenu) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                ) {
                    DividerHorizontal()

                    Button(
                        onClick = {
                        },
                        enabled = false,
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color_0054F3,
                            contentColor = Color.White
                        )
                    ) {
                        Text(
                            text = "Buat Transaksi Baru",
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyLarge,
                        )
                    }
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = painterResource(R.drawable.form_background),
                    sizeToIntrinsics = false,
                    contentScale = ContentScale.Crop
                )
                .padding(innerPadding)
        ) {
            TopBar(title = "Transfer", onBack = {
                navigation(Back)
            })

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Color.White,
                        RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                    )
                    .padding(horizontal = 16.dp, vertical = 24.dp)
            ) {
                if (!fastMenu) {
                    val tabs1 = listOf("Dalam Negeri")
                    var page1 by rememberSaveable { mutableIntStateOf(0) }

                    Row {
                        tabs1.forEachIndexed { index, data ->
                            val selected = page1 == index
                            val fontWeight =
                                if (selected) FontWeight.SemiBold else FontWeight.Normal
                            val color = if (selected) Color_0054F3 else Color_7B90A6

                            Column(
                                modifier = Modifier
                                    .width(IntrinsicSize.Max)
                                    .clickable { page1 = index }
                            ) {
                                Text(
                                    text = data,
                                    color = color,
                                    fontWeight = fontWeight,
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Spacer(modifier = Modifier.height(12.dp))

                                if (selected) {
                                    DividerHorizontal(
                                        thickness = 3.dp,
                                        color = Color_0054F3
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.width(16.dp))
                        }
                    }

                    DividerHorizontal()

                    Spacer(modifier = Modifier.height(24.dp))
                }

                Shimmer(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(44.dp)
                )

                Spacer(modifier = Modifier.height(24.dp))

                Row {
                    Shimmer(
                        modifier = Modifier
                            .width(72.dp)
                            .height(32.dp)
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Shimmer(
                        modifier = Modifier
                            .width(72.dp)
                            .height(32.dp)
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                repeat(3) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Shimmer(
                            modifier = Modifier
                                .size(48.dp)
                                .background(Color_E9EEF6.copy(alpha = alpha), CircleShape)
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            Shimmer(
                                modifier = Modifier
                                    .fillMaxWidth(0.7f)
                                    .height(16.dp)
                            )

                            Spacer(modifier = Modifier.height(4.dp))

                            Shimmer(
                                modifier = Modifier
                                    .fillMaxWidth(0.5f)
                                    .height(14.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTransferFormContent() {
    MaterialTheme {
        TransferFormContent(
            fastMenu = false,
            onBankSelected = { _, _, _ -> },
            transferForm = getSampleTransferFormData(),
            navigation = {},
            viewModel = koinViewModel(),
            inputAccountNumberViewModel = koinViewModel()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTransferFormContentLoading() {
    MaterialTheme {
        TransferFormContentLoading(
            fastMenu = false,
            onBankSelected = { _, _, _ -> },
            navigation = {},
            viewModel = koinViewModel(),
            inputAccountNumberViewModel = koinViewModel()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTransferFormContentFastMenu() {
    MaterialTheme {
        TransferFormContent(
            fastMenu = true,
            onBankSelected = { _, _, _ -> },
            transferForm = getSampleTransferFormData(),
            navigation = {},
            viewModel = koinViewModel(),
            inputAccountNumberViewModel = koinViewModel()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTransferFormContentLoadingFastMenu() {
    MaterialTheme {
        TransferFormContentLoading(
            fastMenu = true,
            onBankSelected = { _, _, _ -> },
            navigation = {},
            viewModel = koinViewModel(),
            inputAccountNumberViewModel = koinViewModel()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTransferFormContentEmpty() {
    MaterialTheme {
        TransferFormContent(
            fastMenu = false,
            onBankSelected = { _, _, _ -> },
            transferForm = getSampleTransferFormData(includeHistory = false, includeSaved = false),
            navigation = {},
            viewModel = koinViewModel(),
            inputAccountNumberViewModel = koinViewModel()
        )
    }
}

private fun getSampleTransferFormData(
    includeHistory: Boolean = true,
    includeSaved: Boolean = true
): TransferFormData {
    return TransferFormData(
        bankList = getSampleBankList(),
        history = if (includeHistory) getSampleHistory() else emptyList(),
        saved = if (includeSaved) getSampleFavorites() else emptyList(),
        aftBanner = AftBanner(),
        referenceNumber = "REF" + System.currentTimeMillis().toString().takeLast(6)
    )
}

private fun getSampleBankList() = listOf(
    BankItem(
        code = "002",
        name = "BANK BRI",
        imageUrl = "https://brimo.bri.co.id/assets/imgs/bank/bri.png",
        isAlphanumeric = true
    ),
    BankItem(
        code = "008",
        name = "BANK MANDIRI",
        imageUrl = "https://brimo.bri.co.id/assets/imgs/bank/mandiri.png",
        isAlphanumeric = false
    ),
    BankItem(
        code = "014",
        name = "BANK BCA",
        imageUrl = "https://brimo.bri.co.id/assets/imgs/bank/bca.png",
        isAlphanumeric = false
    ),
    BankItem(
        code = "009",
        name = "BANK BNI",
        imageUrl = "https://brimo.bri.co.id/assets/imgs/bank/bni.png",
        isAlphanumeric = false
    )
)

private fun getSampleHistory() = listOf(
    HistoryResponse(
        title = "Transfer BRI",
        subtitle = "Bank BRI - Budi Santoso",
        description = "**********",
        iconPath = "https://brimo.bri.co.id/assets/imgs/bank/bri.png",
        value = "002|**********",
        listType = "HISTORY",
        iconName = "bri"
    ),
    HistoryResponse(
        title = "Transfer BCA",
        subtitle = "Bank BCA - Siti Rahayu",
        description = "**********",
        iconPath = "https://brimo.bri.co.id/assets/imgs/bank/bca.png",
        value = "014|**********",
        listType = "HISTORY",
        iconName = "bca"
    )
)

private fun getSampleFavorites() = listOf(
    FavoriteResponse(
        title = "Budi - BRI",
        subtitle = "Bank BRI - Budi Santoso",
        description = "**********",
        iconPath = "https://brimo.bri.co.id/assets/imgs/bank/bri.png",
        value = "FAV|002|**********",
        favorite = true,
        listType = "FAVORITE",
        iconName = "bri",
        keyword = "budi santoso bri"
    ),
    FavoriteResponse(
        title = "Siti - BCA",
        subtitle = "Bank BCA - Siti Rahayu",
        description = "**********",
        iconPath = "https://brimo.bri.co.id/assets/imgs/bank/bca.png",
        value = "FAV|014|**********",
        favorite = false,
        listType = "FAVORITE",
        iconName = "bca",
        keyword = "siti rahayu bca"
    )
)
