package id.co.bri.brimo.payment.feature.qris.ui.scan

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.app.QrisScanRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisScanRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

internal class QrisScanViewModel(
    private val qrisRepository: QrisRepository,
    private val qrTapRepository: QrTapRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisScanRoute = savedStateHandle.toRoute<QrisScanRoute>()

    fun handleEvent(event: QrisScanEvent) {
        when (event) {
            is QrisScanEvent.ScanQr -> {
                postScanQris(stringQr = event.stringQr)
            }

            is QrisScanEvent.PayloadQr -> {
                postQrTapPayload(pin = event.pin)
            }
        }
    }

    private val _qrisScan = MutableSharedFlow<UiState<QrisModel>>()
    val qrisScan = _qrisScan.asSharedFlow()

    private fun postScanQris(stringQr: String) {
        viewModelScope.launch {
            _qrisScan.asUiState {
                val scanQris = qrisRepository.postScanQris(
                    request = QrisScanRequest(stringQr = stringQr),
                    fastMenu = qrisScanRoute.fastMenu
                )

                val accountList = scanQris.accountList?.map { account ->
                    async {
                        try {
                            val saldoNormal = qrisRepository.postSaldoNormal(
                                request = SaldoNormalRequest(
                                    account = account.account.orEmpty()
                                )
                            )
                            account.copy(
                                onHold = saldoNormal.onHold,
                                balance = saldoNormal.balance,
                                balanceString = saldoNormal.balanceString
                            )
                        } catch (_: Throwable) {
                            account
                        }
                    }
                }?.awaitAll()

                val scanQrisUpdated = scanQris.copy(accountList = accountList)

                when (scanQrisUpdated.typeQr) {
                    "qris_mpm", "qris_cb" -> {
                        if (scanQrisUpdated.amountEditable == true) {
                            QrisModel(
                                openPayment = true,
                                typeQr = scanQrisUpdated.typeQr,
                                qrisScan = scanQrisUpdated
                            )
                        } else {
                            val qrisConfirmation =
                                qrisRepository.postQrisConfirmation(
                                    type = scanQrisUpdated.typeQr,
                                    request = QrisConfirmationRequest(
                                        accountNumber = scanQrisUpdated.accountList?.firstOrNull()?.account.orEmpty(),
                                        amount = scanQrisUpdated.payAmount,
                                        note = "",
                                        referenceNumber = scanQrisUpdated.referenceNumber,
                                        saveAs = "",
                                        inputTipAmount = null,
                                    ),
                                    fastMenu = qrisScanRoute.fastMenu
                                )

                            QrisModel(
                                openPayment = scanQrisUpdated.amountEditable,
                                typeQr = scanQrisUpdated.typeQr,
                                qrisScan = scanQrisUpdated,
                                qrisConfirmation = qrisConfirmation
                            )
                        }
                    }

                    "qris_transfer" -> {
                        QrisModel(
                            openPayment = scanQrisUpdated.openPayment,
                            typeQr = scanQrisUpdated.typeQr,
                            qrisScan = scanQrisUpdated
                        )
                    }

                    else -> {
                        QrisModel(
                            openPayment = scanQrisUpdated.openPayment,
                            typeQr = scanQrisUpdated.typeQr,
                            qrisScan = scanQrisUpdated
                        )
                    }
                }
            }
        }
    }

    private val _qrTapPayload = MutableSharedFlow<UiState<QrTapPayloadResponse>>()
    val qrTapPayload = _qrTapPayload.asSharedFlow()

    private fun postQrTapPayload(pin: String) {
        viewModelScope.launch {
            _qrTapPayload.asUiState {
                val payload = qrTapRepository.postQrTapPayload(
                    request = QrTapPayloadRequest(
                        pin = pin,
                        account = "",
                        cardToken = "",
                        nfcType = ""
                    ),
                    fastMenu = qrisScanRoute.fastMenu
                )

                val accountList = payload.accountList?.map { account ->
                    async {
                        try {
                            val saldoNormal = qrTapRepository.postSaldoNormal(
                                request = SaldoNormalRequest(
                                    account = account.account.orEmpty()
                                )
                            )
                            account.copy(
                                onHold = saldoNormal.onHold,
                                balance = saldoNormal.balance,
                                balanceString = saldoNormal.balanceString
                            )
                        } catch (_: Throwable) {
                            account
                        }
                    }
                }?.awaitAll()

                val payloadUpdated = payload.copy(accountList = accountList)

                payloadUpdated
            }
        }
    }
}
