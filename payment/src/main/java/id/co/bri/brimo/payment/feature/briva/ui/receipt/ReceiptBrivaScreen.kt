package id.co.bri.brimo.payment.feature.briva.ui.receipt

import android.Manifest
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.saveToDisk
import id.co.bri.brimo.payment.core.design.component.shareBitmap
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.Color_FDEDED
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.composableToBitmap
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun ReceiptBrivaScreen(
    onFinish: () -> Unit = {},
    receiptViewModel: ReceiptBrivaViewModel = koinViewModel()
) {
    if (receiptViewModel.receiptData != null) {
        ReceiptBrivaContent(
            data = receiptViewModel.receiptData,
            onFinish = onFinish
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun ReceiptBrivaContent(
    data: BrivaPaymentResponse,
    onFinish: () -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // Global
    var visible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(500)
        visible = true
    }

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    val writeStorageAccessState = rememberMultiplePermissionsState(
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            emptyList()
        } else {
            listOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    )

    fun shareBitmapFromComposable(isShare: Boolean) {
        if (writeStorageAccessState.allPermissionsGranted) {
            coroutineScope.launch {
                try {
                    val bitmap = composableToBitmap(
                        mainScope = coroutineScope,
                        activity = context as ComponentActivity,
                        width = configuration.screenWidthDp.dp,
                        screenDensity = density
                    ) {
                        MainTheme {
                            ReceiptBrivaView(data)
                        }
                    }
                    val uri = bitmap.saveToDisk(context)
                    if (isShare) {
                        shareBitmap(context, uri)
                    } else {
                        coroutineScope.launch {
                            snackbarType = SnackbarType.SUCCESS
                            snackbarHostState.showSnackbar("Bukti pembayaran berhasil diunduh.")
                        }
                    }
                } catch (_: Throwable) {
                    if (!isShare) {
                        coroutineScope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar("Bukti pembayaran gagal diunduh.")
                        }
                    }
                }
            }
        } else if (writeStorageAccessState.shouldShowRationale) {
            coroutineScope.launch {
                snackbarType = SnackbarType.INFO
                val result = snackbarHostState.showSnackbar(
                    message = "The storage permission is needed to save the image",
                    actionLabel = "Grant Access"
                )

                if (result == SnackbarResult.ActionPerformed) {
                    writeStorageAccessState.launchMultiplePermissionRequest()
                }
            }
        } else {
            writeStorageAccessState.launchMultiplePermissionRequest()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(durationMillis = 1000)) +
                    slideInVertically(
                        animationSpec = tween(durationMillis = 1000),
                        initialOffsetY = { it / 2 }
                    )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (!(data.onProcess ?: false)) {
                        Image(
                            painter = painterResource(R.drawable.receipt_icon_share),
                            contentDescription = null,
                            modifier = Modifier
                                .size(48.dp)
                                .clickable {
                                    coroutineScope.launch {
                                        shareBitmapFromComposable(true)
                                    }
                                },
                            contentScale = ContentScale.Fit
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Image(
                            painter = painterResource(R.drawable.receipt_icon_download),
                            contentDescription = null,
                            modifier = Modifier
                                .size(48.dp)
                                .clickable {
                                    coroutineScope.launch {
                                        shareBitmapFromComposable(false)
                                    }
                                },
                            contentScale = ContentScale.Fit
                        )

                        Spacer(modifier = Modifier.width(12.dp))
                    }

                    PrimaryButton(
                        label = "Selesai",
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        onFinish()
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .verticalScroll(rememberScrollState())
                    .paint(
                        painter = painterResource(R.drawable.receipt_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
                    .padding(horizontal = 16.dp)
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                AnimatedVisibility(
                    visible = visible,
                    enter = fadeIn(animationSpec = tween(durationMillis = 1000))
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Image(
                            painter = painterResource(R.drawable.image_logo),
                            contentDescription = null,
                            modifier = Modifier
                                .width(62.dp)
                                .height(32.dp)
                                .align(Alignment.CenterHorizontally),
                            contentScale = ContentScale.Fit
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                AnimatedVisibility(
                    visible = visible,
                    enter = fadeIn(animationSpec = tween(durationMillis = 1000)) +
                        slideInVertically(
                            animationSpec = tween(durationMillis = 1000),
                            initialOffsetY = { it / 2 }
                        )
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(
                                    RoundedCornerShape(
                                        topStart = 24.dp,
                                        topEnd = 64.dp,
                                        bottomStart = 24.dp,
                                        bottomEnd = 24.dp
                                    )
                                )
                                .background(Color.White)
                                .paint(
                                    painter = painterResource(R.drawable.image_watermark),
                                    sizeToIntrinsics = false,
                                    contentScale = ContentScale.Crop
                                )
                                .padding(16.dp)
                        ) {
                            val image = if (data.onProcess == true) {
                                R.drawable.image_receipt_process
                            } else {
                                R.drawable.image_receipt_success
                            }

                            Image(
                                painter = painterResource(image),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(120.dp)
                                    .align(Alignment.CenterHorizontally),
                                contentScale = ContentScale.Fit
                            )

                            Text(
                                text = data.title.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.titleLarge
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = data.totalDataView?.firstOrNull()?.value.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.headlineLarge
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = data.dateTransaction.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            Text(
                                text = "Detail Transaksi",
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                                    .padding(horizontal = 16.dp, vertical = 12.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.icon_bri),
                                        modifier = Modifier.size(32.dp),
                                        contentDescription = null,
                                        contentScale = ContentScale.Fit
                                    )

                                    Spacer(modifier = Modifier.width(12.dp))

                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = data.sourceAccountDataView?.title.orEmpty(),
                                            modifier = Modifier.fillMaxWidth(),
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.height(2.dp))

                                        val subtitle =
                                            data.sourceAccountDataView?.subtitle.orEmpty()
                                        val description =
                                            data.sourceAccountDataView?.description.orEmpty()

                                        Text(
                                            text = "$subtitle - $description",
                                            modifier = Modifier.fillMaxWidth(),
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.receipt_icon_payment),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                            .size(16.dp),
                                        contentScale = ContentScale.Fit
                                    )

                                    Spacer(modifier = Modifier.width(12.dp))

                                    DividerHorizontal()
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    ImageAsync(
                                        context = context,
                                        url = data.billingDetail?.iconPath.orEmpty(),
                                        initial = data.billingDetail?.title.orEmpty(),
                                        size = 32
                                    )

                                    Spacer(modifier = Modifier.width(12.dp))

                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = data.billingDetail?.title.orEmpty(),
                                            modifier = Modifier.fillMaxWidth(),
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.height(2.dp))

                                        val subtitle = data.billingDetail?.subtitle.orEmpty()
                                        val description = data.billingDetail?.description.orEmpty()

                                        Text(
                                            text = "$subtitle - $description",
                                            modifier = Modifier.fillMaxWidth(),
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            data.headerDataView?.forEach { item ->
                                Row(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = item.name.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        color = Color_7B90A6,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))

                                    Text(
                                        text = item.value.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        textAlign = TextAlign.End,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            data.dataViewTransaction?.forEach { item ->
                                Row(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = item.name.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        color = Color_7B90A6,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))

                                    Text(
                                        text = item.value.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        textAlign = TextAlign.End,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            if (!data.amountDataView.isNullOrEmpty()) {
                                DividerHorizontal()

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            data.amountDataView?.forEach { item ->
                                Row(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = item.name.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        color = Color_7B90A6,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))

                                    Text(
                                        text = item.value.orEmpty(),
                                        modifier = Modifier.weight(1f),
                                        textAlign = TextAlign.End,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            var showDetail by rememberSaveable { mutableStateOf(false) }
                            val textDetail by remember {
                                derivedStateOf {
                                    if (showDetail) "Sembunyikan" else "Lihat Detail"
                                }
                            }
                            val iconDetail by remember {
                                derivedStateOf {
                                    if (showDetail) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown
                                }
                            }

                            if (showDetail) {
                                DividerHorizontal()

                                Spacer(modifier = Modifier.height(12.dp))

                                Text(
                                    text = "Informasi Hubungi Call Center 1500017",
                                    modifier = Modifier.fillMaxWidth(),
                                    color = Color_7B90A6,
                                    textAlign = TextAlign.Center,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.height(16.dp))

                                Text(
                                    text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                                        "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                                        "Kantor Pusat BRI - Jakarta Pusat\n" +
                                        "NPWP : 01.001.608.7-093.000",
                                    modifier = Modifier.fillMaxWidth(),
                                    color = Color_7B90A6,
                                    textAlign = TextAlign.Center,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.height(12.dp))
                            }

                            Row(
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .clickable {
                                        showDetail = !showDetail
                                    },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = textDetail,
                                    modifier = Modifier.padding(start = 4.dp),
                                    color = Color_0054F3,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Icon(
                                    imageVector = iconDetail,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                    tint = Color_0054F3
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(24.dp))

                        Text(
                            text = "Produk Pilihan Untukmu",
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        LazyRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            items(2) {
                                Column(
                                    modifier = Modifier
                                        .width(250.dp)
                                        .background(Color.White, RoundedCornerShape(16.dp))
                                        .padding(16.dp)
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.thumbnail),
                                        contentDescription = null,
                                        modifier = Modifier.size(32.dp),
                                        contentScale = ContentScale.Crop
                                    )

                                    Spacer(modifier = Modifier.height(12.dp))

                                    Row(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "Token Listrik",
                                            modifier = Modifier.weight(1f),
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.width(4.dp))

                                        Text(
                                            text = "Cashback 100rb",
                                            modifier = Modifier
                                                .background(Color_FDEDED, RoundedCornerShape(16.dp))
                                                .padding(horizontal = 8.dp, vertical = 2.dp),
                                            color = Color_E84040,
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.labelSmall
                                        )
                                    }

                                    Spacer(modifier = Modifier.height(4.dp))

                                    Text(
                                        text = "Beli token listrik di Qitta, langsung dapet Cashback s.d. 100rb!",
                                        modifier = Modifier.fillMaxWidth(),
                                        style = MaterialTheme.typography.bodySmall
                                    )

                                    Spacer(modifier = Modifier.height(12.dp))

                                    Text(
                                        text = "Beli Sekarang",
                                        color = Color_0054F3,
                                        fontWeight = FontWeight.SemiBold,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@Preview(heightDp = 1100)
@Composable
private fun PreviewReceiptBriva() {
    MainTheme {
        ReceiptBrivaContent(
            data = BrivaPaymentResponse(
                amountDataView = listOf(
                    DataViewResponse(
                        name = "Nominal",
                        value = "Rp10.000",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Biaya Admin",
                        value = "Rp1.000",
                        style = ""
                    )
                ),
                billingDetail = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                closeButtonString = "",
                dataViewTransaction = listOf(
                    DataViewResponse(
                        name = "Jenis Transaksi",
                        value = "Pembayaran BRIVA",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Keterangan",
                        value = "Test",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Catatan",
                        value = "Test",
                        style = ""
                    )
                ),
                dateTransaction = "25 Juni 2025, 12:34 WIB",
                footer = "",
                footerHtml = "",
                headerDataView = listOf(
                    DataViewResponse(
                        name = "No. Ref",
                        value = "**********",
                        style = ""
                    )
                ),
                helpFlag = false,
                immediatelyFlag = false,
                onProcess = false,
                referenceNumber = "",
                rowDataShow = 0,
                share = false,
                shareButtonString = "",
                sourceAccountDataView = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                sourceAccountListType = "",
                title = "Transaksi Berhasil",
                titleImage = "",
                totalDataView = listOf(
                    DataViewResponse(
                        name = "Total Transaksi",
                        value = "Rp11.000",
                        style = ""
                    )
                ),
                voucherDataView = listOf()
            )
        )
    }
}
