package id.co.bri.brimo.payment.dependency

import android.content.Intent
import android.nfc.Tag
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import id.co.bri.brimo.payment.core.network.BaseResponse
import id.co.bri.brimo.payment.core.network.request.ListCityRequest
import id.co.bri.brimo.payment.core.network.request.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.TransferConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.TransferInquiryRequest
import id.co.bri.brimo.payment.core.network.request.TransferPayRequest
import id.co.bri.brimo.payment.core.network.response.BrizziCheckBalanceResponse
import id.co.bri.brimo.payment.core.network.response.BrizziConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.ListCityResponse
import id.co.bri.brimo.payment.core.network.response.SaldoNormalData
import id.co.bri.brimo.payment.core.network.response.TransferConfirmationData
import id.co.bri.brimo.payment.core.network.response.TransferFormData
import id.co.bri.brimo.payment.core.network.response.TransferInquiryData
import id.co.bri.brimo.payment.core.network.response.TransferPayResponse
import id.co.bri.brimo.payment.feature.brizzi.data.model.CardModel

interface PaymentApi {

    suspend fun hitApi(url: String, request: Any, fastMenu: Boolean): String

    fun initCheckBalance(
        tag: Tag,
        onSuccess: (String, String) -> Unit,
        onError: (Throwable) -> Unit
    )

    fun commitCheckBalance(
        response: BrizziCheckBalanceResponse,
        onSuccess: (BrizziCheckBalanceResponse, CardModel) -> Unit,
        onError: (Throwable) -> Unit
    )

    fun scanPayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        confirmationResponse: BrizziConfirmationResponse,
        pin: String,
        fastMenu: Boolean
    )

    fun activatePayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        fastMenu: Boolean
    )

    fun onPin(activity: ComponentActivity)

    fun onSession(activity: ComponentActivity)

    fun navigateToWallet(activity: ComponentActivity, fastMenu: Boolean)

    fun navigateToBpjs(activity: ComponentActivity, fastMenu: Boolean)

    fun navigateToTravel(activity: ComponentActivity, fastMenu: Boolean)

    suspend fun postTransferForm(): BaseResponse<TransferFormData>

    suspend fun postTransferInquiry(request: TransferInquiryRequest): BaseResponse<TransferInquiryData>

    suspend fun postTransferConfirmation(request: TransferConfirmationRequest): BaseResponse<TransferConfirmationData>

    suspend fun postTransferPay(request: TransferPayRequest): BaseResponse<TransferPayResponse>

    suspend fun postSaldoNormal(request: SaldoNormalRequest): BaseResponse<SaldoNormalData>

    suspend fun postListCity(request: ListCityRequest): BaseResponse<ListCityResponse>
}
