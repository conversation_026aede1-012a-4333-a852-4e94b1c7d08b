package id.co.bri.brimo.payment.feature.qrtap.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrTapRoute
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import java.util.Locale

@Composable
internal fun QrTapScreen(
    navigation: (QrTapNavigation) -> Unit = {},
    qrTapViewModel: QrTapViewModel = koinViewModel()
) {
    val accountList by qrTapViewModel.accountList.collectAsStateWithLifecycle()

    QrTapContent(
        state = QrTapState(
            qrTapRoute = qrTapViewModel.qrTapRoute,
            accountList = accountList.orEmpty(),
        ),
        event = qrTapViewModel::handleEvent,
        navigation = navigation
    )
}

@Composable
private fun QrTapContent(
    state: QrTapState,
    event: (QrTapEvent) -> Unit = {},
    navigation: (QrTapNavigation) -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()

    // Global
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.firstOrNull())
    }
    val arrowComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.arrows_animation)
    )
    var expireTime by rememberSaveable { mutableIntStateOf(300) }
    val expireText by remember {
        derivedStateOf {
            String.format(
                Locale.getDefault(),
                "%02d:%02d",
                expireTime / 60,
                expireTime % 60
            )
        }
    }
    val expired by remember {
        derivedStateOf {
            expireTime == 0
        }
    }

    LaunchedEffect(expireTime) {
        if (expireTime > 0) {
            delay(1000)
            expireTime--
        }
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {

        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                nominal = "",
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrTapEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrTapRoute.fastMenu
            )
        }
    }

    LaunchedEffect(Unit) {

    }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.park_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrTapNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "QRIS Tap",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                Image(
                    painter = painterResource(R.drawable.image_tap),
                    contentDescription = null,
                    modifier = Modifier
                        .width(270.dp)
                        .height(240.dp)
                        .align(Alignment.CenterHorizontally),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Bayar dengan QRIS Tap",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "Dekatkan atau tempel HP ke mesin pembayaran",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Marsela Satya",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Text(
                        text = "BritAma - 0290 **** **** 112",
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Row(
                        modifier = Modifier.align(Alignment.CenterHorizontally),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(R.drawable.flag_indonesia),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            contentScale = ContentScale.Fit
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Text(
                            text = "Rp5.000.000",
                            fontWeight = FontWeight.SemiBold,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.headlineSmall
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Image(
                            painter = painterResource(R.drawable.icon_unhide_eye),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            contentScale = ContentScale.Fit
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Row(
                        modifier = Modifier.align(Alignment.CenterHorizontally),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Diproses transaksi",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Image(
                            painter = painterResource(R.drawable.icon_clock),
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            contentScale = ContentScale.Fit
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = expireText,
                            color = Color_0054F3,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .pointerInput(Unit) {
                                detectVerticalDragGestures(
                                    onVerticalDrag = { change, dragAmount ->
                                        if (dragAmount <= -27) {
                                            accountBottomSheet = true
                                        }
                                    }
                                )
                            }
                    ) {
                        Spacer(modifier = Modifier.height(24.dp))

                        LottieAnimation(
                            composition = arrowComposition,
                            modifier = Modifier
                                .width(24.dp)
                                .height(32.dp)
                                .align(Alignment.CenterHorizontally),
                            iterations = LottieConstants.IterateForever
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Geser untuk ganti sumber dana",
                            modifier = Modifier.fillMaxWidth(),
                            color = Color_0054F3,
                            fontWeight = FontWeight.SemiBold,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(24.dp))
                    }

                    Spacer(modifier = Modifier.height(24.dp))
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewQrTap() {
    MainTheme {
        QrTapContent(
            state = QrTapState(
                qrTapRoute = QrTapRoute(
                    data = "",
                    fastMenu = false
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
