package id.co.bri.brimo.payment.feature.qrtap.ui

import id.co.bri.brimo.payment.app.QrTapRoute
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse

internal data class QrTapState(
    val qrTapRoute: QrTapRoute,
    val accountList: List<AccountResponse> = emptyList(),
)

internal sealed class QrTapEvent {
    data class RefreshSaldo(
        val account: String
    ) : QrTapEvent()
}

internal sealed class QrTapNavigation {
    object Back : QrTapNavigation()
}
