package id.co.bri.brimo.payment.app

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.briva.ui.confirmation.BrivaConfirmationNavigation
import id.co.bri.brimo.payment.feature.briva.ui.confirmation.BrivaConfirmationScreen
import id.co.bri.brimo.payment.feature.briva.ui.favorite.BrivaFavoriteNavigation
import id.co.bri.brimo.payment.feature.briva.ui.favorite.BrivaFavoriteScreen
import id.co.bri.brimo.payment.feature.briva.ui.form.BrivaFormNavigation
import id.co.bri.brimo.payment.feature.briva.ui.form.BrivaFormScreen
import id.co.bri.brimo.payment.feature.briva.ui.nominal.BrivaNominalNavigation
import id.co.bri.brimo.payment.feature.briva.ui.nominal.BrivaNominalScreen
import id.co.bri.brimo.payment.feature.briva.ui.receipt.ReceiptBrivaScreen
import id.co.bri.brimo.payment.feature.brizzi.ui.confirmation.BrizziConfirmationNavigation
import id.co.bri.brimo.payment.feature.brizzi.ui.confirmation.BrizziConfirmationScreen
import id.co.bri.brimo.payment.feature.brizzi.ui.edit.EditFavoriteNavigation
import id.co.bri.brimo.payment.feature.brizzi.ui.edit.EditFavoriteScreen
import id.co.bri.brimo.payment.feature.brizzi.ui.form.BrizziFormNavigation
import id.co.bri.brimo.payment.feature.brizzi.ui.form.BrizziFormScreen
import id.co.bri.brimo.payment.feature.brizzi.ui.nominal.BrizziNominalNavigation
import id.co.bri.brimo.payment.feature.brizzi.ui.nominal.BrizziNominalScreen
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.ReceiptScreen
import id.co.bri.brimo.payment.feature.qris.ui.confirmation.QrisConfirmationNavigation
import id.co.bri.brimo.payment.feature.qris.ui.confirmation.QrisConfirmationScreen
import id.co.bri.brimo.payment.feature.qris.ui.nominal.QrisCrossNominalScreen
import id.co.bri.brimo.payment.feature.qris.ui.nominal.QrisNominalNavigation
import id.co.bri.brimo.payment.feature.qris.ui.nominal.QrisScanNominalScreen
import id.co.bri.brimo.payment.feature.qris.ui.nominal.QrisTransferNominalScreen
import id.co.bri.brimo.payment.feature.qris.ui.park.confirmation.QrisParkConfirmationNavigation
import id.co.bri.brimo.payment.feature.qris.ui.park.confirmation.QrisParkConfirmationScreen
import id.co.bri.brimo.payment.feature.qris.ui.park.status.QrisParkStatusScreen
import id.co.bri.brimo.payment.feature.qris.ui.receipt.ReceiptQrisScreen
import id.co.bri.brimo.payment.feature.qris.ui.scan.QrisScanNavigation
import id.co.bri.brimo.payment.feature.qris.ui.scan.QrisScanScreen
import id.co.bri.brimo.payment.feature.qrshow.ui.form.QrShowFormNavigation
import id.co.bri.brimo.payment.feature.qrshow.ui.form.QrShowFormScreen
import id.co.bri.brimo.payment.feature.qrshow.ui.generate.QrShowGenerateNavigation
import id.co.bri.brimo.payment.feature.qrshow.ui.generate.QrShowGenerateScreen
import id.co.bri.brimo.payment.feature.qrtap.ui.QrTapNavigation
import id.co.bri.brimo.payment.feature.qrtap.ui.QrTapScreen
import id.co.bri.brimo.payment.feature.qrtransfer.ui.qrtransfer.QrTransferNavigation
import id.co.bri.brimo.payment.feature.qrtransfer.ui.qrtransfer.QrTransferScreen
import id.co.bri.brimo.payment.feature.receipt.ReceiptDailyBankingScreen
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationNavigation
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationScreen
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.form.EditFavoriteTransferScreen
import id.co.bri.brimo.payment.feature.transfer.ui.form.TransferFormNavigation
import id.co.bri.brimo.payment.feature.transfer.ui.form.TransferFormScreen
import id.co.bri.brimo.payment.feature.transfer.ui.form.TransferFormViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.input.accountnumber.InputAccountNumberNavigation
import id.co.bri.brimo.payment.feature.transfer.ui.input.accountnumber.InputAccountNumberScreen
import id.co.bri.brimo.payment.feature.transfer.ui.input.accountnumber.InputAccountNumberViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.input.nominal.TransferInputNominalNavigation
import id.co.bri.brimo.payment.feature.transfer.ui.input.nominal.TransferInputNominalScreen
import id.co.bri.brimo.payment.feature.transfer.ui.input.nominal.TransferInputNominalViewModel
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun PaymentNavigation(
    navController: NavHostController,
    uri: Uri?,
    extras: Bundle?,
    activity: ComponentActivity
) {
    val route = handleDeeplink(uri, extras)
    val fastMenu = extras?.getBoolean("fromfastmenu", false) ?: false
    val transferFormViewModel: TransferFormViewModel = koinViewModel()
    val inputAccountNumberViewModel: InputAccountNumberViewModel = koinViewModel()
    val transferInputNominalViewModel: TransferInputNominalViewModel = koinViewModel()
    val transferConfirmationViewModel: TransferConfirmationViewModel = koinViewModel()

    PaymentDependency.setFinish {
        navController.navigate(route) {
            popUpTo(route) { inclusive = false }
            launchSingleTop = true
        }
    }

    NavHost(
        navController = navController,
        startDestination = route
    ) {
        composable<BrizziFormRoute> { entry ->
            val favorite = entry.savedStateHandle.get<String>("favorite")
            BrizziFormScreen(
                activity = activity,
                favorite = favorite,
                onFavorite = {
                    entry.savedStateHandle.remove<String>("favorite")
                },
                navigation = { nav ->
                    when (nav) {
                        BrizziFormNavigation.Back -> {
                            activity.finish()
                        }

                        is BrizziFormNavigation.Favorite -> {
                            navController.navigate(EditFavoriteRoute(nav.number, nav.name, ""))
                        }

                        is BrizziFormNavigation.Nominal -> {
                            navController.navigate(
                                BrizziNominalRoute(
                                    nav.brizziData,
                                    nav.fastMenu,
                                    nav.fromScan
                                )
                            )
                        }
                    }
                }
            )
        }
        composable<BrizziNominalRoute> {
            BrizziNominalScreen(
                activity = activity,
                navigation = { nav ->
                    when (nav) {
                        BrizziNominalNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is BrizziNominalNavigation.Confirmation -> {
                            navController.navigate(
                                ConfirmationRoute(
                                    nav.data,
                                    nav.fastMenu,
                                    nav.fromScan
                                )
                            )
                        }
                    }
                }
            )
        }
        composable<EditFavoriteRoute> {
            EditFavoriteScreen(
                navigation = { nav ->
                    when (nav) {
                        EditFavoriteNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is EditFavoriteNavigation.Save -> {
                            navController
                                .previousBackStackEntry
                                ?.savedStateHandle
                                ?.set("favorite", nav.message)
                            navController
                                .previousBackStackEntry
                                ?.savedStateHandle
                                ?.set("name", nav.name)
                            navController.popBackStack()
                        }
                    }
                }
            )
        }
        composable<ConfirmationRoute> {
            BrizziConfirmationScreen(
                activity = activity,
                navigation = { nav ->
                    when (nav) {
                        BrizziConfirmationNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is BrizziConfirmationNavigation.Payment -> {
                            navController.navigate(ReceiptRoute(nav.data)) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }
        composable<ReceiptRoute> {
            ReceiptScreen(
                onFinish = {
                    activity.finish()
                },
                transferConfirmationViewModel = transferConfirmationViewModel,
            )
        }
        composable<TransferFormRoute> { backStackEntry ->
//            val fastMenu = backStackEntry.toRoute<TransferFormRoute>()
            TransferFormScreen(
                fastMenu = fastMenu,
                onBankSelected = { bankCode, bankName, imageUrl ->
                    navController.navigate(
                        InputAccountNumberRoute(
                            bankCode,
                            bankName,
                            imageUrl,
                            isAddFavorite = false
                        )
                    )
                },
                navigation = { nav ->
                    when (nav) {
                        TransferFormNavigation.Back -> {
                            activity.finish()
                        }

                        is TransferFormNavigation.EditFavorite -> {
                            navController.navigate(
                                EditFavoriteTransferRoute(nav.isAddFavorite)
                            )
                        }

                        is TransferFormNavigation.ToInputNominal -> {
                            navController.navigate(
                                TransferInputNominalRoute(
                                    imageUrl = nav.imageUrl,
                                    favName = nav.favName
                                )
                            )
                        }

                        is TransferFormNavigation.AddFavorite -> {
                            navController.navigate(
                                InputAccountNumberRoute(
                                    nav.bankCode,
                                    nav.bankName,
                                    nav.imageUrl,
                                    nav.isAddFavorite
                                )
                            )
                        }

                        TransferFormNavigation.BackToTransferForm -> {
                            navController.navigate(TransferFormRoute(fastMenu)) {
                                inputAccountNumberViewModel.clearTransferInquiry()
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                },
                transferFormViewModel = transferFormViewModel,
                inputAccountNumberViewModel = inputAccountNumberViewModel
            )
        }
        composable<BrivaFormRoute> { entry ->
            val savedId = entry.savedStateHandle.get<String>("savedId")
            val name = entry.savedStateHandle.get<String>("name")
            val favorite = if (savedId != null && name != null) {
                Pair(savedId, name)
            } else {
                null
            }
            BrivaFormScreen(
                favorite = favorite,
                onFavorite = {
                    entry.savedStateHandle.remove<String>("savedId")
                    entry.savedStateHandle.remove<String>("name")
                },
                navigation = { nav ->
                    when (nav) {
                        BrivaFormNavigation.Back -> {
                            activity.finish()
                        }

                        is BrivaFormNavigation.EditFavorite -> {
                            navController.navigate(
                                BrivaFavoriteRoute(
                                    number = nav.number,
                                    name = nav.name,
                                    savedId = nav.savedId,
                                    subtitle = nav.subtitle
                                )
                            )
                        }

                        is BrivaFormNavigation.Nominal -> {
                            navController.navigate(
                                BrivaNominalRoute(
                                    data = nav.brivaData,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }

                        is BrivaFormNavigation.Confirmation -> {
                            navController.navigate(
                                BrivaConfirmationRoute(
                                    data = nav.brivaData,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }
                    }
                }
            )
        }
        composable<BrivaFavoriteRoute> {
            BrivaFavoriteScreen(
                navigation = { nav ->
                    when (nav) {
                        BrivaFavoriteNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is BrivaFavoriteNavigation.Edit -> {
                            navController
                                .previousBackStackEntry
                                ?.savedStateHandle
                                ?.set("savedId", nav.savedId)
                            navController
                                .previousBackStackEntry
                                ?.savedStateHandle
                                ?.set("name", nav.name)
                            navController.popBackStack()
                        }
                    }
                }
            )
        }
        composable<BrivaNominalRoute> {
            BrivaNominalScreen(
                navigation = { nav ->
                    when (nav) {
                        BrivaNominalNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is BrivaNominalNavigation.Confirmation -> {
                            navController.navigate(
                                BrivaConfirmationRoute(
                                    data = nav.brivaData,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }
                    }
                }
            )
        }
        composable<BrivaConfirmationRoute> { entry ->
            BrivaConfirmationScreen(
                navigation = { nav ->
                    when (nav) {
                        is BrivaConfirmationNavigation.Payment -> {
                            navController.navigate(ReceiptBrivaRoute(data = nav.data)) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }
        composable<ReceiptBrivaRoute> {
            ReceiptBrivaScreen(
                onFinish = {
                    activity.finish()
                }
            )
        }

        composable<QrShowFormRoute> { entry ->
            QrShowFormScreen(
                navigation = { nav ->
                    when (nav) {
                        QrShowFormNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is QrShowFormNavigation.Generate -> {
                            if (nav.inclusive) {
                                navController.navigate(
                                    QrShowGenerateRoute(
                                        nav.data,
                                        nav.fastMenu
                                    )
                                ) {
                                    popUpTo(entry.destination.route.orEmpty()) { inclusive = true }
                                }
                            } else {
                                navController.navigate(QrShowGenerateRoute(nav.data, nav.fastMenu))
                            }
                        }
                    }
                }
            )
        }

        composable<QrShowGenerateRoute> { entry ->
            QrShowGenerateScreen(
                navigation = { nav ->
                    when (nav) {
                        is QrShowGenerateNavigation.Back -> {
                            navController.navigateUp()
                        }

                        is QrShowGenerateNavigation.Delete -> {
                            if (nav.inclusive) {
                                navController.navigate(QrShowFormRoute(nav.fastMenu)) {
                                    popUpTo(entry.destination.route.orEmpty()) { inclusive = true }
                                }
                            } else {
                                navController.navigateUp()
                            }

                        }

                        is QrShowGenerateNavigation.Payment -> {
                            navController.navigate(ReceiptQrisRoute(data = nav.data, type = "")) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }

        composable<QrTransferRoute> {
            QrTransferScreen(
                navigation = { nav ->
                    when (nav) {
                        QrTransferNavigation.Back -> {
                            navController.navigateUp()
                        }

                        QrTransferNavigation.History -> {

                        }
                    }
                }
            )
        }

        composable<QrTapRoute> {
            QrTapScreen(
                navigation = { nav ->
                    when (nav) {
                        QrTapNavigation.Back -> {
                            navController.navigateUp()
                        }
                    }
                }
            )
        }

        composable<InputAccountNumberRoute> { backStackEntry ->
            val route = backStackEntry.toRoute<InputAccountNumberRoute>()

            InputAccountNumberScreen(
                activity = activity,
                bankCode = route.bankCode,
                bankName = route.bankName,
                imageUrl = route.imageUrl,
                isAddFavorite = route.isAddFavorite,
                transferFormViewModel = transferFormViewModel,
                inputAccountNumberViewModel = inputAccountNumberViewModel,
                isFastMenu = fastMenu,
                navigation = { nav ->
                    when (nav) {
                        is InputAccountNumberNavigation.Next -> {
                            navController.navigate(
                                TransferInputNominalRoute(
                                    imageUrl = nav.imageUrl,
                                    favName = nav.favName
                                )
                            )
                        }

                        is InputAccountNumberNavigation.Back -> {
                            inputAccountNumberViewModel.clearTransferInquiry()
                            navController.navigateUp()
                        }

                        is InputAccountNumberNavigation.AddFavorite -> {
                            navController.navigate(
                                EditFavoriteTransferRoute(
                                    isAddFavorite = nav.isAddFavorite
                                )
                            )
                        }
                    }
                },
                onBack = {
                    inputAccountNumberViewModel.clearTransferInquiry()
                    navController.navigateUp()
                },
                onContinue = { accountNumber ->
//                    navController.navigate(
//                        TransferInputNominalRoute(
//                            accountNumber = accountNumber,
//                            bankCode = route.bankCode,
//                            bankName = route.bankName
//                        )
//                    )
                }
            )
        }

        composable<TransferInputNominalRoute> { backStackEntry ->
            val route = backStackEntry.toRoute<TransferInputNominalRoute>()
            TransferInputNominalScreen(
                activity = activity,
                navigation = { nav ->
                    when (nav) {
                        TransferInputNominalNavigation.Back -> {
                            inputAccountNumberViewModel.clearTransferInquiry()
                            navController.navigateUp()
                        }

                        is TransferInputNominalNavigation.Confirmation -> {
                            navController.navigate(
                                TransferConfirmationRoute(
                                    isRtgs = nav.isRtgs,
                                    isFav = nav.isFav
                                )
                            )
                        }
                    }
                },
                viewModel = transferInputNominalViewModel,
                inputAccountNumberViewModel = inputAccountNumberViewModel,
                bankImageUrl = route.imageUrl,
                favName = route.favName,
                isFastMenu = fastMenu
            )
        }

        composable<TransferConfirmationRoute> { backStackEntry ->
            val route = backStackEntry.toRoute<TransferConfirmationRoute>()
            TransferConfirmationScreen(
                activity = activity,
                transferInputNominalViewModel = transferInputNominalViewModel,
                transferConfirmationViewModel = transferConfirmationViewModel,
                isRtgs = route.isRtgs,
                isFav = route.isFav,
                isFastMenu = fastMenu,
                navigation = { nav ->
                    when (nav) {
                        TransferConfirmationNavigation.Back -> {
                            navController.navigate(TransferFormRoute(fastMenu)) {
                                inputAccountNumberViewModel.clearTransferInquiry()
                                transferInputNominalViewModel.clearConfirmationData()
                                transferConfirmationViewModel.clearPaymentData()
                                popUpTo(0) { inclusive = true }
                            }
                        }

                        is TransferConfirmationNavigation.Payment -> {
                            navController.navigate(ReceiptBrivaRoute(nav.data)) {
                                popUpTo(0) { inclusive = true }
                            }
                        }

                        is TransferConfirmationNavigation.PaymentRtgs -> {
                            navController.navigate(ReceiptBrivaRoute(nav.data)) {
                                popUpTo(0) { inclusive = true }
                            }
                        }

                        is TransferConfirmationNavigation.BackToTransferForm -> {
                            navController.navigate(TransferFormRoute(fastMenu)) {
                                inputAccountNumberViewModel.clearTransferInquiry()
                                transferInputNominalViewModel.clearConfirmationData()
                                transferConfirmationViewModel.clearPaymentData()
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }

        composable<QrisScanRoute> { entry ->
            QrisScanScreen(
                navigation = { nav ->
                    when (nav) {
                        QrisScanNavigation.Back -> {
                            activity.finish()
                        }

                        is QrisScanNavigation.QrShow -> {
                            navController.navigate(QrShowFormRoute(fastMenu))
                        }

                        QrisScanNavigation.QrTransfer -> {
                            navController.navigate(QrTransferRoute)
                        }

                        is QrisScanNavigation.QrTap -> {
                            navController.navigate(
                                QrTapRoute(
                                    data = nav.qrisData,
                                    fastMenu = fastMenu
                                )
                            )
                        }

                        is QrisScanNavigation.Nominal -> {
                            navController.navigate(
                                QrisNominalRoute(
                                    data = nav.qrisData,
                                    type = nav.type,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }

                        is QrisScanNavigation.Confirmation -> {
                            navController.navigate(
                                QrisConfirmationRoute(
                                    data = nav.qrisData,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }

                        is QrisScanNavigation.Park -> {
                            navController.navigate(
                                QrisParkConfirmationRoute(
                                    data = nav.qrisData,
                                    fastMenu = nav.fastMenu
                                )
                            )
                        }
                    }
                }
            )
        }

        composable<QrisNominalRoute> { entry ->
            val qrisNominalRoute = entry.toRoute<QrisNominalRoute>()
            when (qrisNominalRoute.type) {
                "qris_mpm" -> {
                    QrisScanNominalScreen(
                        navigation = { nav ->
                            when (nav) {
                                QrisNominalNavigation.Back -> {
                                    navController.navigateUp()
                                }

                                is QrisNominalNavigation.Confirmation -> {
                                    navController.navigate(
                                        QrisConfirmationRoute(
                                            data = nav.qrisData,
                                            fastMenu = nav.fastMenu
                                        )
                                    )
                                }
                            }
                        }
                    )
                }

                "qris_cb" -> {
                    QrisCrossNominalScreen(
                        navigation = { nav ->
                            when (nav) {
                                QrisNominalNavigation.Back -> {
                                    navController.navigateUp()
                                }

                                is QrisNominalNavigation.Confirmation -> {
                                    navController.navigate(
                                        QrisConfirmationRoute(
                                            data = nav.qrisData,
                                            fastMenu = nav.fastMenu
                                        )
                                    )
                                }
                            }
                        }
                    )
                }

                "qris_transfer" -> {
                    QrisTransferNominalScreen(
                        navigation = { nav ->
                            when (nav) {
                                QrisNominalNavigation.Back -> {
                                    navController.navigateUp()
                                }

                                is QrisNominalNavigation.Confirmation -> {
                                    navController.navigate(
                                        QrisConfirmationRoute(
                                            data = nav.qrisData,
                                            fastMenu = nav.fastMenu
                                        )
                                    )
                                }
                            }
                        }
                    )
                }

                else -> {
                    navController.navigateUp()
                }
            }
        }

        composable<QrisConfirmationRoute> {
            QrisConfirmationScreen(
                navigation = { nav ->
                    when (nav) {
                        is QrisConfirmationNavigation.Payment -> {
                            navController.navigate(
                                ReceiptQrisRoute(
                                    data = nav.data,
                                    type = nav.type
                                )
                            ) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }

        composable<ReceiptQrisRoute> {
            ReceiptQrisScreen(
                onFinish = {
                    activity.finish()
                }
            )
        }

        composable<QrisParkConfirmationRoute> {
            QrisParkConfirmationScreen(
                navigation = { nav ->
                    when (nav) {
                        is QrisParkConfirmationNavigation.Status -> {
                            navController.navigate(QrisParkStatusRoute(nav.qrisData)) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }

        composable<QrisParkStatusRoute> {
            QrisParkStatusScreen(
                onFinish = {
                    activity.finish()
                }
            )
        }

        composable<EditFavoriteTransferRoute> { backStackEntry ->
            val route = backStackEntry.toRoute<EditFavoriteTransferRoute>()
            EditFavoriteTransferScreen(
                navigation = { nav ->
                    when (nav) {
                        TransferFormNavigation.Back -> {
                            inputAccountNumberViewModel.clearTransferInquiry()
                            navController.navigate(TransferFormRoute(fastMenu)) {
                                popUpTo(0) { inclusive = true }
                                transferFormViewModel.postTransferForm(fastMenu)
                                transferFormViewModel.successEditFavoriteState()
                            }
                        }

                        TransferFormNavigation.BackToTransferForm -> {
                            inputAccountNumberViewModel.clearTransferInquiry()
                            navController.navigate(TransferFormRoute(fastMenu)) {
                                popUpTo(0) { inclusive = true }
                                transferFormViewModel.postTransferForm(fastMenu)
                                transferFormViewModel.successAddFavoriteState()
                            }
                        }

                        else -> {}
                    }
                },
                viewModel = transferFormViewModel,
                inputAccountNumberViewModel = inputAccountNumberViewModel,
                isAddFavorite = route.isAddFavorite
            )
        }


        composable<ReceiptDailyBankingRoute> {
            ReceiptDailyBankingScreen(
                onFinish = {
                    activity.finish()
                }
            )
        }
    }
}
