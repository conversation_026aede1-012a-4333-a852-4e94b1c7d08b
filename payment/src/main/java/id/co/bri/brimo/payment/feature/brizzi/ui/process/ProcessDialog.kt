package id.co.bri.brimo.payment.feature.brizzi.ui.process

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import kotlinx.coroutines.delay

@Composable
internal fun ProcessDialog() {
    val planeComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.animasi_pesawat)
    )
    var visible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(500)
        visible = true
        delay(2000)
        visible = false
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(R.drawable.process_background),
                sizeToIntrinsics = false,
                contentScale = ContentScale.Crop
            ),
        contentAlignment = Alignment.Center
    ) {
        LottieAnimation(
            composition = planeComposition,
            modifier = Modifier
                .padding(bottom = 200.dp)
                .fillMaxWidth()
                .aspectRatio(1f)
        )

        Column(
            modifier = Modifier
                .padding(top = 200.dp)
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
        ) {
            AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(durationMillis = 500)) +
                    slideInVertically(
                        animationSpec = tween(durationMillis = 500),
                        initialOffsetY = { it / 2 }
                    ),
                exit = fadeOut(animationSpec = tween(durationMillis = 500)) +
                    slideOutVertically(
                        animationSpec = tween(durationMillis = 500),
                        targetOffsetY = { it / 2 }
                    )
            ) {
                Text(
                    text = "Transaksi sedang diproses",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.headlineMedium
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(durationMillis = 500)) +
                    slideInVertically(
                        animationSpec = tween(durationMillis = 500),
                        initialOffsetY = { it / 2 }
                    ),
                exit = fadeOut(animationSpec = tween(durationMillis = 500)) +
                    slideOutVertically(
                        animationSpec = tween(durationMillis = 500),
                        targetOffsetY = { it / 2 }
                    )
            ) {
                Text(
                    text = "Transaksi ini akan selesai dalam beberapa saat",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewProcess() {
    MainTheme {
        ProcessDialog()
    }
}
