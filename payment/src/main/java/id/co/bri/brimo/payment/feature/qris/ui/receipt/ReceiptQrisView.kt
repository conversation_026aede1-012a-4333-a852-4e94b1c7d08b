package id.co.bri.brimo.payment.feature.qris.ui.receipt

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.ReceiptQrisRoute
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisPaymentResponse

@OptIn(ExperimentalPermissionsApi::class)
@Composable
internal fun ReceiptQrisView(
    route: ReceiptQrisRoute,
    data: QrisPaymentResponse
) {
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .paint(
                painter = painterResource(R.drawable.receipt_background),
                sizeToIntrinsics = false,
                contentScale = ContentScale.Crop
            )
            .padding(horizontal = 16.dp)
    ) {
        Spacer(modifier = Modifier.height(24.dp))

        Image(
            painter = painterResource(R.drawable.image_logo),
            contentDescription = null,
            modifier = Modifier
                .width(62.dp)
                .height(32.dp)
                .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.height(24.dp))

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(
                    RoundedCornerShape(
                        topStart = 24.dp,
                        topEnd = 64.dp,
                        bottomStart = 24.dp,
                        bottomEnd = 24.dp
                    )
                )
                .background(Color.White)
                .paint(
                    painter = painterResource(R.drawable.image_watermark),
                    sizeToIntrinsics = true,
                    contentScale = ContentScale.Crop
                )
                .padding(16.dp)
        ) {
            val image = if (data.onProcess == true) {
                R.drawable.image_receipt_process
            } else {
                R.drawable.image_receipt_success
            }

            Image(
                painter = painterResource(image),
                contentDescription = null,
                modifier = Modifier
                    .size(120.dp)
                    .align(Alignment.CenterHorizontally),
                contentScale = ContentScale.Fit
            )

            Text(
                text = data.title.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleLarge
            )

            Spacer(modifier = Modifier.height(8.dp))

            val amount = if (route.type == "qris_cb") {
                data.amountDataView?.firstOrNull()?.value.orEmpty()
            } else {
                data.totalDataView?.firstOrNull()?.value.orEmpty()
            }

            Text(
                text = amount,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineLarge
            )

            if (route.type == "qris_cb") {
                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Senilai dengan ${data.totalDataView?.firstOrNull()?.value.orEmpty()}",
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            val date = if (route.type == "qris_cb") {
                data.headerDataView?.firstOrNull()?.value.orEmpty()
            } else {
                data.dateTransaction.orEmpty()
            }

            Text(
                text = date,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Detail Transaksi",
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_bri),
                        modifier = Modifier.size(32.dp),
                        contentDescription = null,
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.sourceAccountDataView?.title.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        val subtitle =
                            data.sourceAccountDataView?.subtitle.orEmpty()
                        val description =
                            data.sourceAccountDataView?.description.orEmpty()
                        val source =
                            subtitle + if (description.isNotEmpty()) " - $description" else ""

                        Text(
                            text = source,
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.receipt_icon_payment),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .size(16.dp),
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    DividerHorizontal()
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ImageAsync(
                        context = context,
                        url = data.billingDetail?.iconPath.orEmpty(),
                        initial = data.billingDetail?.title.orEmpty(),
                        size = 32
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.billingDetail?.title.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        val subtitle = data.billingDetail?.subtitle.orEmpty()
                        val description = data.billingDetail?.description.orEmpty()
                        val source = subtitle +
                            if (description.isNotEmpty()) " - $description" else ""

                        Text(
                            text = source,
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            data.headerDataView?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            data.dataViewTransaction?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            data.kursDataView?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            if (!data.amountDataView.isNullOrEmpty()) {
                DividerHorizontal()

                Spacer(modifier = Modifier.height(12.dp))
            }

            data.amountDataView?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            DividerHorizontal()

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Informasi Hubungi Call Center 1500017",
                modifier = Modifier.fillMaxWidth(),
                color = Color_7B90A6,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                    "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                    "Kantor Pusat BRI - Jakarta Pusat\n" +
                    "NPWP : 01.001.608.7-093.000",
                modifier = Modifier.fillMaxWidth(),
                color = Color_7B90A6,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "© 2025 PT. Bank Rakyat Indonesia (Persero), Tbk.",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Text(
            text = "Terdaftar dan diawasi oleh Otoritas Jasa Keuangan",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Preview(heightDp = 1000)
@Composable
private fun PreviewReceiptQrisView() {
    MainTheme {
        ReceiptQrisView(
            route = ReceiptQrisRoute(
                data = "",
                type = "qris_transfer"
            ),
            data = QrisPaymentResponse(
                amountDataView = listOf(
                    DataViewResponse(
                        name = "Nominal",
                        value = "Rp10.000",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Biaya Admin",
                        value = "Rp1.000",
                        style = ""
                    )
                ),
                billingDetail = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                closeButtonString = "",
                dataViewTransaction = listOf(
                    DataViewResponse(
                        name = "Jenis Transaksi",
                        value = "Pembayaran QRIS",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Keterangan",
                        value = "Test",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Catatan",
                        value = "Test",
                        style = ""
                    )
                ),
                dateTransaction = "25 Juni 2025, 12:34 WIB",
                footer = "",
                footerHtml = "",
                headerDataView = listOf(
                    DataViewResponse(
                        name = "No. Ref",
                        value = "**********",
                        style = ""
                    )
                ),
                helpFlag = false,
                immediatelyFlag = false,
                onProcess = false,
                referenceNumber = "",
                rowDataShow = 0,
                share = false,
                shareButtonString = "",
                sourceAccountDataView = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                sourceAccountListType = "",
                title = "Transaksi Berhasil",
                titleImage = "",
                totalDataView = listOf(
                    DataViewResponse(
                        name = "Total Transaksi",
                        value = "Rp11.000",
                        style = ""
                    )
                ),
                voucherDataView = listOf(),
                kursDataView = listOf()
            )
        )
    }
}
