package id.co.bri.brimo.payment.core.design.component

import androidx.compose.animation.animateColor
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.MainTheme

internal fun Modifier.shimmer(): Modifier = composed {
    val infiniteTransition = rememberInfiniteTransition(label = INFINITE_TRANSITION)
    val color by infiniteTransition.animateColor(
        initialValue = Color_E9EEF6,
        targetValue = Color_E9EEF6.copy(alpha = 0.2f),
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = DURATION_MILLIS, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = COLOR
    )

    drawBehind {
        drawRoundRect(color = color, cornerRadius = CornerRadius(100f))
    }
}

@Composable
internal fun Shimmer(modifier: Modifier = Modifier) {
    Box(modifier = modifier.shimmer())
}

private const val INFINITE_TRANSITION = "infiniteTransition"
private const val COLOR = "color"
private const val DURATION_MILLIS = 800

@Preview
@Composable
private fun PreviewShimmer() {
    MainTheme {
        Shimmer(
            modifier = Modifier
                .width(100.dp)
                .height(10.dp)
        )
    }
}
