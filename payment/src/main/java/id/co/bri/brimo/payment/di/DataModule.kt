package id.co.bri.brimo.payment.di

import id.co.bri.brimo.payment.feature.briva.data.api.BrivaRepository
import id.co.bri.brimo.payment.feature.briva.data.impl.BrivaRepositoryImpl
import id.co.bri.brimo.payment.feature.brizzi.data.api.BrizziRepository
import id.co.bri.brimo.payment.feature.brizzi.data.impl.BrizziRepositoryImpl
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.impl.QrisRepositoryImpl
import id.co.bri.brimo.payment.feature.qrshow.data.api.QrShowRepository
import id.co.bri.brimo.payment.feature.qrshow.data.impl.QrShowRepositoryImpl
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import id.co.bri.brimo.payment.feature.qrtap.data.impl.QrTapRepositoryImpl
import id.co.bri.brimo.payment.feature.qrtransfer.data.api.QrTransferRepository
import id.co.bri.brimo.payment.feature.qrtransfer.data.impl.QrTransferRepositoryImpl
import id.co.bri.brimo.payment.feature.transfer.data.api.TransferRepository
import id.co.bri.brimo.payment.feature.transfer.data.impl.TransferRepositoryImpl
import kotlinx.coroutines.CoroutineDispatcher
import org.koin.core.qualifier.named
import org.koin.dsl.module

internal val dataModule = module {

    single<BrizziRepository> {
        BrizziRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<BrivaRepository> {
        BrivaRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<QrTransferRepository> {
        QrTransferRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<QrShowRepository> {
        QrShowRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<QrTapRepository> {
        QrTapRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<TransferRepository> {
        TransferRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }

    single<QrisRepository> {
        QrisRepositoryImpl(
            get<CoroutineDispatcher>(named(IO))
        )
    }
}
