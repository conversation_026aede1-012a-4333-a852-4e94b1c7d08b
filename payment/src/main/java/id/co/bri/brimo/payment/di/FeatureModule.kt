package id.co.bri.brimo.payment.di

import id.co.bri.brimo.payment.feature.briva.ui.confirmation.BrivaConfirmationViewModel
import id.co.bri.brimo.payment.feature.briva.ui.favorite.BrivaFavoriteViewModel
import id.co.bri.brimo.payment.feature.briva.ui.form.BrivaFormViewModel
import id.co.bri.brimo.payment.feature.briva.ui.nominal.BrivaNominalViewModel
import id.co.bri.brimo.payment.feature.briva.ui.receipt.ReceiptBrivaViewModel
import id.co.bri.brimo.payment.feature.brizzi.ui.confirmation.BrizziConfirmationViewModel
import id.co.bri.brimo.payment.feature.brizzi.ui.edit.EditFavoriteViewModel
import id.co.bri.brimo.payment.feature.brizzi.ui.form.BrizziFormViewModel
import id.co.bri.brimo.payment.feature.brizzi.ui.nominal.BrizziNominalViewModel
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.ReceiptViewModel
import id.co.bri.brimo.payment.feature.qris.ui.confirmation.QrisConfirmationViewModel
import id.co.bri.brimo.payment.feature.qris.ui.nominal.QrisNominalViewModel
import id.co.bri.brimo.payment.feature.qris.ui.park.confirmation.QrisParkConfirmationViewModel
import id.co.bri.brimo.payment.feature.qris.ui.park.status.QrisParkStatusViewModel
import id.co.bri.brimo.payment.feature.qris.ui.receipt.ReceiptQrisViewModel
import id.co.bri.brimo.payment.feature.qris.ui.scan.QrisScanViewModel
import id.co.bri.brimo.payment.feature.qrshow.ui.form.QrShowFormViewModel
import id.co.bri.brimo.payment.feature.qrshow.ui.generate.QrShowGenerateViewModel
import id.co.bri.brimo.payment.feature.qrtap.ui.QrTapViewModel
import id.co.bri.brimo.payment.feature.qrtransfer.ui.qrtransfer.QrTransferViewModel
import id.co.bri.brimo.payment.feature.receipt.ReceiptDailyBankingViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.form.TransferFormViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.input.accountnumber.InputAccountNumberViewModel
import id.co.bri.brimo.payment.feature.transfer.ui.input.nominal.TransferInputNominalViewModel
import org.koin.androidx.viewmodel.dsl.viewModelOf
import org.koin.dsl.module

internal val featureModule = module {

    viewModelOf(::BrizziFormViewModel)
    viewModelOf(::EditFavoriteViewModel)
    viewModelOf(::BrizziNominalViewModel)
    viewModelOf(::BrizziConfirmationViewModel)
    viewModelOf(::ReceiptViewModel)

    viewModelOf(::BrivaFormViewModel)
    viewModelOf(::BrivaFavoriteViewModel)
    viewModelOf(::BrivaNominalViewModel)
    viewModelOf(::BrivaConfirmationViewModel)
    viewModelOf(::ReceiptBrivaViewModel)

    viewModelOf(::QrTransferViewModel)
    viewModelOf(::QrShowFormViewModel)
    viewModelOf(::QrShowGenerateViewModel)
    viewModelOf(::QrTapViewModel)

    viewModelOf(::QrisScanViewModel)
    viewModelOf(::QrisNominalViewModel)
    viewModelOf(::QrisConfirmationViewModel)
    viewModelOf(::ReceiptQrisViewModel)
    viewModelOf(::QrisParkConfirmationViewModel)
    viewModelOf(::QrisParkStatusViewModel)
    viewModelOf(::TransferInputNominalViewModel)
    viewModelOf(::TransferFormViewModel)
    viewModelOf(::InputAccountNumberViewModel)
    viewModelOf(::TransferConfirmationViewModel)

    viewModelOf(::ReceiptDailyBankingViewModel)
}
