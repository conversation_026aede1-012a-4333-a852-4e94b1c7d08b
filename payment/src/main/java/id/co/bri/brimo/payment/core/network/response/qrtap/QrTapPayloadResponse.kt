package id.co.bri.brimo.payment.core.network.response.qrtap

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse

internal data class QrTapPayloadResponse(
    @SerializedName("account_no") val accountNo: String?,
    @SerializedName("card_no") val cardNo: String?,
    @SerializedName("card_holder") val cardHolder: String?,
    @SerializedName("nfc_type") val nfcType: String?,
    @SerializedName("nfc_type_image") val nfcTypeImage: String?,
    @SerializedName("payload") val payload: String?,
    @SerializedName("active_for") val activeFor: Int?,
    @SerializedName("card_token") val cardToken: String?,
    @SerializedName("tap_guidance") val tapGuidance: String?,
    @SerializedName("is_cc") val isCc: Boolean?,
    @SerializedName("account_list") val accountList: List<AccountResponse>?,
    @SerializedName("credit_card_list") val creditCardList: List<AccountResponse>?
)
