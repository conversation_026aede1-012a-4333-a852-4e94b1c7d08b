package id.co.bri.brimo.payment.feature.transfer.ui.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.EmptyState
import id.co.bri.brimo.payment.core.design.component.OutlinedTextFieldCustom
import id.co.bri.brimo.payment.core.design.component.outlinedTextFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.base.HistoryResponse
import id.co.bri.brimo.payment.feature.briva.ui.base.ItemBrivaFavorite
import id.co.bri.brimo.payment.feature.briva.ui.base.ItemBrivaHistory

@Composable
internal fun TransferSearchDialog(
    favorite: List<FavoriteResponse>,
    history: List<HistoryResponse>,
    isFastMenu: Boolean = false,
    onOptionFavorite: (FavoriteResponse) -> Unit = {},
    onSelectFavorite: (FavoriteResponse) -> Unit = {},
    onSelectHistory: (HistoryResponse) -> Unit = {},
    onBack: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(R.drawable.form_background),
                sizeToIntrinsics = false,
                contentScale = ContentScale.Crop
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(R.drawable.icon_back),
                contentDescription = null,
                modifier = Modifier
                    .size(32.dp)
                    .clickable {
                        onBack()
                    },
                contentScale = ContentScale.Fit
            )

            Text(
                text = "Transfer",
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
                    .padding(end = 32.dp),
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleLarge
            )
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Color.White,
                    RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                )
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            var searchField by rememberSaveable { mutableStateOf("") }

            OutlinedTextFieldCustom(
                value = searchField,
                onValueChange = { searchField = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp),
                textStyle = MaterialTheme.typography.bodyMedium,
                placeholder = {
                    Text(
                        text = "Cari tujuan transaksi",
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                    )
                },
                trailingIcon = if (searchField.isNotEmpty()) {
                    {
                        Icon(
                            painter = painterResource(R.drawable.icon_close),
                            contentDescription = null,
                            modifier = Modifier
                                .size(16.dp)
                                .clickable {
                                    searchField = ""
                                }
                        )
                    }
                } else {
                    null
                },
                singleLine = true,
                shape = RoundedCornerShape(24.dp),
                colors = outlinedTextFieldColors(),
                contentPadding = PaddingValues(horizontal = 16.dp)
            )

            Spacer(modifier = Modifier.height(24.dp))

            val filteredFavorite =
                favorite.filter {  it.title.orEmpty().contains(searchField, true) ||
                        it.subtitle.orEmpty().contains(searchField, true) ||
                        it.description.orEmpty().contains(searchField, true)}
            val filteredHistory =
                history.filter {  it.title.orEmpty().contains(searchField, true) ||
                        it.subtitle.orEmpty().contains(searchField, true) ||
                        it.description.orEmpty().contains(searchField, true) }
            val emptyData =
                searchField.isNotEmpty() && filteredFavorite.isEmpty() && filteredHistory.isEmpty()

            if (emptyData) {
                Spacer(modifier = Modifier.height(24.dp))

                EmptyState(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    title = "Hasil Tidak Ditemukan",
                    description = "Coba gunakan kata kunci lainnya, ya."
                )
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    if (filteredFavorite.isNotEmpty()) {
                        Text(
                            text = "Favorit",
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        filteredFavorite.forEachIndexed { index, item ->
                            Spacer(modifier = Modifier.height(16.dp))

                            ItemBrivaFavorite(
                                highlight = searchField,
                                item = item,
                                onOption = onOptionFavorite,
                                onSelect = onSelectFavorite,
                                showOption = !isFastMenu
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            if (index < filteredFavorite.size - 1) {
                                DividerHorizontal()
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    if (filteredHistory.isNotEmpty()) {
                        Text(
                            text = "Riwayat",
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        filteredHistory.forEachIndexed { index, item ->
                            Spacer(modifier = Modifier.height(16.dp))

                            ItemBrivaHistory(
                                highlight = searchField,
                                item = item,
                                onSelect = onSelectHistory
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            if (index < filteredHistory.size - 1) {
                                DividerHorizontal()
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewTransferSearch() {
    MainTheme {
        TransferSearchDialog(
            favorite = listOf(
                FavoriteResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description",
                    value = "",
                    favorite = false,
                    keyword = ""
                )
            ),
            history = listOf(
                HistoryResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description",
                    value = ""
                )
            )
        )
    }
}
