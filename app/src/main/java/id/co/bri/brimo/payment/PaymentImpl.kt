package id.co.bri.brimo.payment

import android.content.Intent
import android.nfc.Tag
import android.nfc.tech.IsoDep
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import id.co.bri.brimo.data.api.ApiService
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.rx.AppSchedulerProvider
import id.co.bri.brimo.payment.core.network.BaseResponse
import id.co.bri.brimo.payment.core.network.request.ListCityRequest
import id.co.bri.brimo.payment.core.network.request.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.TransferConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.TransferInquiryRequest
import id.co.bri.brimo.payment.core.network.request.TransferPayRequest
import id.co.bri.brimo.payment.core.network.response.BrizziCheckBalanceResponse
import id.co.bri.brimo.payment.core.network.response.BrizziConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.ListCityResponse
import id.co.bri.brimo.payment.core.network.response.SaldoNormalData
import id.co.bri.brimo.payment.core.network.response.TransferConfirmationData
import id.co.bri.brimo.payment.core.network.response.TransferFormData
import id.co.bri.brimo.payment.core.network.response.TransferInquiryData
import id.co.bri.brimo.payment.core.network.response.TransferPayResponse
import id.co.bri.brimo.payment.dependency.PaymentApi
import id.co.bri.brimo.payment.feature.brizzi.data.model.CardModel
import id.co.bri.brimo.ui.activities.CekBrizziDuaActivity
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.FormBpjsActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.TapBrizziAktivasiActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity.isFromFastMenu
import id.co.bri.brimo.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.FormDompetDigitalRevamp
import id.co.bri.brimo.ui.activities.travel.TravelMenuActivity
import id.co.bri.brizzi.Brizzi
import id.co.bri.brizzi.CardData
import id.co.bri.brizzi.callbacks.BrizziCallback
import id.co.bri.brizzi.exception.BrizziException
import io.reactivex.disposables.CompositeDisposable
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import java.util.concurrent.TimeUnit

class PaymentImpl(
    private val apiService: ApiService,
    private val brimoPrefSource: BRImoPrefSource,
    private val gson: Gson,
    private val apiSource: ApiSource
) : PaymentApi {

    val schedulerProvider = AppSchedulerProvider()

    override suspend fun hitApi(url: String, request: Any, fastMenu: Boolean): String {
        val compositeDisposable = CompositeDisposable()
        val seqNum = brimoPrefSource.seqNumber
        val requestCheck = if (request is String && request == "") {
            JsonObject().apply {
                addProperty("username", "")
            }
        } else {
            request
        }
        val requestString = gson.toJson(requestCheck)
        val requestUpdated = if (fastMenu) {
            val jsonElement = JsonParser.parseString(requestString)
            val jsonObject = jsonElement.asJsonObject
            jsonObject.addProperty("username", brimoPrefSource.username)
            jsonObject.addProperty("token_key", brimoPrefSource.tokenKey)
            jsonObject.toString()
        } else {
            requestString
        }

        return callbackFlow {
            compositeDisposable.add(
                apiSource.getDataString(url, requestUpdated, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribe(
                        { response ->
                            val restResponse = MapperHelper.stringToRestResponse(response, seqNum)
                            val stringResponse = gson.toJson(restResponse)
                            trySend(stringResponse)
                        },
                        { error ->
                            close(error)
                        }
                    )
            )

            awaitClose {
                compositeDisposable.dispose()
            }
        }.first()
    }

    private lateinit var brizzi: Brizzi

    override fun initCheckBalance(
        tag: Tag,
        onSuccess: (String, String) -> Unit,
        onError: (Throwable) -> Unit
    ) {
        val brizziTag = IsoDep.get(tag)
        brizzi = Brizzi(brizziTag)

        brizzi.initCheckbalance(object : BrizziCallback {
            override fun onSuccess(cardData: CardData) {
                onSuccess(cardData.cardNumber, cardData.randomSAM)
            }

            override fun onFailure(error: BrizziException) {
                onError(Exception(error.errorCode, Exception(error.message)))
            }
        })
    }

    override fun commitCheckBalance(
        response: BrizziCheckBalanceResponse,
        onSuccess: (BrizziCheckBalanceResponse, CardModel) -> Unit,
        onError: (Throwable) -> Unit
    ) {
        brizzi.commitCheckBalance(response.key + response.rcHost, object : BrizziCallback {
            override fun onSuccess(cardData: CardData) {
                val cardModel = CardModel(
                    cardNumber = cardData.cardNumber,
                    cardBalance = cardData.cardBalance,
                    cardStatus = cardData.cardStatus,
                    validateRandom = cardData.validateRandom,
                    cardHistory = cardData.cardHistory.map {
                        CardModel.History(
                            type = it.trxType,
                            date = it.trxDate,
                            time = it.trxTime,
                            amount = it.amount
                        )
                    }
                )
                onSuccess(response, cardModel)
            }

            override fun onFailure(error: BrizziException) {
                onError(Exception(error.errorCode, Exception(error.message)))
            }
        })
    }

    override fun scanPayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        confirmationResponse: BrizziConfirmationResponse,
        pin: String,
        fastMenu: Boolean
    ) {
        val response = Gson().toJson(confirmationResponse)
        CekBrizziDuaActivity.launchIntent(
            launcher,
            activity,
            response,
            pin,
            fastMenu
        )
    }

    override fun activatePayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        TapBrizziAktivasiActivity.launchIntent(
            launcher,
            activity,
            fastMenu
        )
    }

    override fun onPin(activity: ComponentActivity) {
        LupaPinActivity.launchIntent(activity)
    }

    override fun onSession(activity: ComponentActivity) {
        FastMenuNewSkinActivity.launchIntentSessionEnd(activity, "")
    }

    override fun navigateToWallet(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        FormDompetDigitalReskinActivity.launchIntent(activity, fastMenu)
    }

    override fun navigateToBpjs(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        // FormBpjsActivity.launchIntent(activity, fastMenu)
    }

    override fun navigateToTravel(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        // TravelMenuActivity.launchIntent(activity)
    }

    override suspend fun postTransferForm(): BaseResponse<TransferFormData> {
        return getDataWithRequest<TransferFormData>(
            apiService,
            brimoPrefSource,
            gson,
            "eljUiVbI9JLjhFYWwOz2ASLYy9XI9l2Qkgx4iopjimk=",
            ""
        )
//       return BaseResponse(
//           code = "00",
//           description = "Sukses",
//           data = TransferFormData(
//               bankList = listOf(
//                   BankItem(
//                       code = "002",
//                       name = "BANK BRI",
//                       isAlphanumeric = true
//                   ),
//                   BankItem(
//                       code = "008",
//                       name = "BANK MANDIRI",
//                       isAlphanumeric = false
//                   ),
//                   BankItem(
//                       code = "009",
//                       name = "BANK BCA",
//                       isAlphanumeric = false
//                   )
//               )
//           )
//       )
    }

    override suspend fun postTransferInquiry(
        request: TransferInquiryRequest
    ): BaseResponse<TransferInquiryData> {
        return getDataWithRequest<TransferInquiryData>(
            apiService,
            brimoPrefSource,
            gson,
            "6kIpoRGvU1pq0D5kH/UB8EyJU7wbx4tk7bNiJnWPkqI=",
            request
        )

        // Sesama BRI
//         return BaseResponse(
//             code = "00",
//             description = "Sukses",
//             data = TransferInquiryData(
//                 accountList = listOf(
//                     AccountItem(
//                         account = "***************",
//                         accountString = "0230 0113 7097 505",
//                         name = "ADIXXXXXXXXXXXXXXLTI",
//                         currency = "Rp",
//                         cardNumber = "5221XXXXXXXX7777",
//                         cardNumberString = "5221 XXXX XXXX 7777",
//                         productType = "BritAma",
//                         accountType = "SA",
//                         scCode = "TA",
//                         default = 0,
//                         alias = "",
//                         minimumBalance = 50000,
//                         limit = -1,
//                         limitString = "",
//                         imageName = "BritAma.png",
//                         imagePath = "http://*************:4010/brimo-asset/account_logo/BritAma.png"
//                     ),
//                     AccountItem(
//                         account = "***************",
//                         accountString = "0230 0100 1674 308",
//                         name = "Infinite",
//                         currency = "Rp",
//                         cardNumber = "5221XXXXXXXX7777",
//                         cardNumberString = "5221 XXXX XXXX 7777",
//                         productType = "Simpedes",
//                         accountType = "SA",
//                         scCode = "SU",
//                         default = 0,
//                         alias = "",
//                         minimumBalance = 25000,
//                         limit = -1,
//                         limitString = "",
//                         imageName = "Simpedes-Umum.png",
//                         imagePath = "http://*************:4010/brimo-asset/account_logo/Simpedes-Umum.png"
//                     )
//                 ),
//                 billingDetail = emptyList(),
//                 billingDetailOpen = listOf(
//                     BillingDetailOpen(
//                         listType = "name",
//                         iconName = "",
//                         iconPath = "",
//                         recipientAvatar = "",
//                         title = "RINXXXXXXXXXANI",
//                         subtitle = "BANK BRI",
//                         description = "0026 0103 4364 503"
//                     )
//                 ),
//                 billingAmount = listOf(
//                     BillingAmount(
//                         name = "Total",
//                         value = "Rp0",
//                         style = ""
//                     )
//                 ),
//                 billingAmountDetail = listOf(
//                     BillingAmount(
//                         name = "Nominal Transfer",
//                         value = "Rp0",
//                         style = ""
//                     ),
//                     BillingAmount(
//                         name = "Biaya Admin",
//                         value = "Rp0",
//                         style = ""
//                     )
//                 ),
//                 openPayment = true,
//                 isBilling = false,
//                 minimumPayment = false,
//                 rowDataShow = 0,
//                 saved = "",
//                 amount = 0,
//                 amountString = "Rp0",
//                 adminFee = 0,
//                 adminFeeString = "Rp0",
//                 adminFeeRtgs = 30000,
//                 adminFeeRtgsString = "Rp30.000",
//                 payAmount = 0,
//                 payAmountString = "Rp0",
//                 minimumAmount = 0,
//                 minimumAmountString = "Rp0",
//                 minimumTransaction = 10000,
//                 minimumTransactionString = "Rp10.000",
//                 noteEnable = true,
//                 transferMethod = emptyList(),
//                 bicCode = "BRINIDJA",
//                 frequencyList = listOf(
//                     FrequencyItem(
//                         code = "once",
//                         value = "Sekali"
//                     ),
//                     FrequencyItem(
//                         code = "weekly",
//                         value = "Mingguan"
//                     ),
//                     FrequencyItem(
//                         code = "monthly",
//                         value = "Bulanan"
//                     )
//                 ),
//                 aftTerm = "Transfer otomatis akan dijadwalkan mulai pukul <b>05.00 WIB.</b> Pastikan saldomu cukup, ya.",
//                 aftEnable = true,
//                 aftBanner = AftBanner(
//                     title = "Pakai Transfer Terjadwal",
//                     desc = "Buat transfermu otomatis sesuai jadwal untuk penerima & nominal yang tertera di atas."
//                 ),
//                 unavailableAftMethod = null,
//                 referenceNumber = "************"
//             )
//         )

        //AntarBank
//        return BaseResponse(
//            code = "00",
//            description = "Sukses",
//            data = TransferInquiryData(
//                accountList = listOf(
//                    AccountItem(
//                        account = "***************",
//                        accountString = "0230 0113 7097 505",
//                        name = "ADIXXXXXXXXXXXXXXLTI",
//                        currency = "Rp",
//                        cardNumber = "5221XXXXXXXX7777",
//                        cardNumberString = "5221 XXXX XXXX 7777",
//                        productType = "BritAma",
//                        accountType = "SA",
//                        scCode = "TA",
//                        default = 0,
//                        alias = "",
//                        minimumBalance = 50000,
//                        limit = -1,
//                        limitString = "",
//                        imageName = "BritAma.png",
//                        imagePath = "http://*************:4010/brimo-asset/account_logo/BritAma.png"
//                    ),
//                    AccountItem(
//                        account = "***************",
//                        accountString = "0230 0100 1674 308",
//                        name = "Infinite",
//                        currency = "Rp",
//                        cardNumber = "5221XXXXXXXX7777",
//                        cardNumberString = "5221 XXXX XXXX 7777",
//                        productType = "Simpedes",
//                        accountType = "SA",
//                        scCode = "SU",
//                        default = 0,
//                        alias = "",
//                        minimumBalance = 25000,
//                        limit = -1,
//                        limitString = "",
//                        imageName = "Simpedes-Umum.png",
//                        imagePath = "http://*************:4010/brimo-asset/account_logo/Simpedes-Umum.png"
//                    )
//                ),
//                billingDetail = emptyList(),
//                billingDetailOpen = listOf(
//                    BillingDetailOpen(
//                        listType = "name",
//                        iconName = "",
//                        iconPath = "",
//                        recipientAvatar = "",
//                        title = "BARXXXXXXXXXXXUMA",
//                        subtitle = "BANK BCA",
//                        description = "0052 0005 7731 00"
//                    )
//                ),
//                billingAmount = listOf(
//                    BillingAmount(
//                        name = "Total",
//                        value = "Rp2.500",
//                        style = ""
//                    )
//                ),
//                billingAmountDetail = listOf(
//                    BillingAmount(
//                        name = "Nominal Transfer",
//                        value = "Rp0",
//                        style = ""
//                    ),
//                    BillingAmount(
//                        name = "Biaya Admin",
//                        value = "Rp2.500",
//                        style = ""
//                    )
//                ),
//                openPayment = true,
//                isBilling = false,
//                minimumPayment = false,
//                rowDataShow = 0,
//                saved = "",
//                amount = 0,
//                amountString = "Rp0",
//                adminFee = 2500,
//                adminFeeString = "Rp2.500",
//                adminFeeRtgs = 30000,
//                adminFeeRtgsString = "Rp30.000",
//                payAmount = 2500,
//                payAmountString = "Rp2.500",
//                minimumAmount = 0,
//                minimumAmountString = "Rp0",
//                minimumTransaction = 10000,
//                minimumTransactionString = "Rp10.000",
//                noteEnable = true,
//                transferMethod = listOf(
//                    TransferMethod(
//                        active = true,
//                        code = "bifast",
//                        title = "Transfer BI-FAST",
//                        subtitle = "Metode transfer online antar bank peserta BI- FAST",
//                        details = listOf(
//                            TransferMethodDetail(
//                                name = "Limit Harian",
//                                style = "",
//                                value = "Rp500.000.000"
//                            ),
//                            TransferMethodDetail(
//                                name = "Limit Per Transaksi",
//                                style = "",
//                                value = "Rp250.000.000"
//                            ),
//                            TransferMethodDetail(
//                                name = "Biaya Admin",
//                                style = "",
//                                value = "Rp2.500"
//                            ),
//                            TransferMethodDetail(
//                                name = "Jam Operasional",
//                                style = "",
//                                value = "24 Jam, 7 hari"
//                            )
//                        ),
//                        form = "",
//                        dropdown = ""
//                    ),
//                    TransferMethod(
//                        active = true,
//                        code = "online",
//                        title = "Transfer Online",
//                        subtitle = "Metode transfer antar bank, realtime",
//                        details = listOf(
//                            TransferMethodDetail(
//                                name = "Limit Harian",
//                                style = "",
//                                value = "Rp250.000.000"
//                            ),
//                            TransferMethodDetail(
//                                name = "Biaya Admin",
//                                style = "",
//                                value = "Rp6.500"
//                            ),
//                            TransferMethodDetail(
//                                name = "Jam Operasional",
//                                style = "",
//                                value = "24 Jam, 7 hari"
//                            )
//                        ),
//                        form = "",
//                        dropdown = ""
//                    ),
//                    TransferMethod(
//                        active = true,
//                        code = "rtgs",
//                        title = "Transfer RTGS",
//                        subtitle = "Metode transfer antar bank dengan nominal lebih dari Rp100 juta",
//                        details = listOf(
//                            TransferMethodDetail(
//                                name = "Limit Harian",
//                                style = "",
//                                value = "Rp550.000.000"
//                            ),
//                            TransferMethodDetail(
//                                name = "Biaya Admin",
//                                style = "",
//                                value = "Rp30.000"
//                            ),
//                            TransferMethodDetail(
//                                name = "Jam Operasional",
//                                style = "",
//                                value = "Senin - Jumat (08.00 - 14.00 WIB)"
//                            )
//                        ),
//                        form = "",
//                        dropdown = ""
//                    )
//                ),
//                bicCode = "CENAIDJA",
//                frequencyList = listOf(
//                    FrequencyItem(
//                        code = "once",
//                        value = "Sekali"
//                    ),
//                    FrequencyItem(
//                        code = "weekly",
//                        value = "Mingguan"
//                    ),
//                    FrequencyItem(
//                        code = "monthly",
//                        value = "Bulanan"
//                    )
//                ),
//                aftTerm = "Transfer otomatis akan dijadwalkan mulai pukul <b>05.00 WIB.</b> Pastikan saldomu cukup, ya.",
//                aftEnable = true,
//                aftBanner = AftBanner(
//                    title = "Pakai Transfer Terjadwal",
//                    desc = "Buat transfermu otomatis sesuai jadwal untuk penerima & nominal yang tertera di atas."
//                ),
//                unavailableAftMethod = listOf(
//                    UnavailableAftMethod(
//                        banner = AftBanner(
//                            title = "Pakai Transfer Terjadwal",
//                            desc = "Metode RTGS tidak dapat digunakan untuk Transfer Terjadwal. Coba pilih metode lain, ya."
//                        ),
//                        code = "rtgs"
//                    )
//                ),
//                referenceNumber = "323534084236"
//            )
//        )

    }

    override suspend fun postTransferConfirmation(
        request: TransferConfirmationRequest
    ): BaseResponse<TransferConfirmationData> {
        return getDataWithRequest<TransferConfirmationData>(
            apiService,
            brimoPrefSource,
            gson,
            "3cr4XBHUmdl5TO88cOFzf5qtEjLU9LsYS0LrKwtAN/Y=",
            request
        )
//        return BaseResponse(
//            code = "00",
//            description = "Sukses",
//            data = TransferConfirmationData(
//                immediatelyFlag = false,
//                sourceAccountDataView = AccountDataView(
//                    listType = "name",
//                    iconName = "",
//                    iconPath = "",
//                    title = "ADIXXXXXXXXXXXXXXLTI",
//                    subtitle = "BRI",
//                    description = "0230 0113 7099 507"
//                ),
//                billingDetail = BillingDetail(
//                    listType = "name",
//                    iconName = "",
//                    iconPath = "",
//                    recipientAvatar = "",
//                    title = "RINXXXXXXXXXANI",
//                    subtitle = "BANK BRI",
//                    description = "0026 0103 4364 503"
//                ),
//                detailDataView = listOf(
//                    DataView(
//                        name = "Catatan",
//                        value = "-",
//                        style = ""
//                    ),
//                    DataView(
//                        name = "Nominal",
//                        value = "Rp10.000",
//                        style = ""
//                    ),
//                    DataView(
//                        name = "Biaya Admin",
//                        value = "Rp0",
//                        style = ""
//                    )
//                ),
//                totalDataView = listOf(
//                    DataView(
//                        name = "Total",
//                        value = "Rp10.000",
//                        style = ""
//                    )
//                ),
//                saveAs = "",
//                amount = 10000,
//                amountString = "Rp10.000",
//                adminFee = 0,
//                adminFeeString = "Rp0",
//                payAmount = 10000,
//                payAmountString = "Rp10.000",
//                pfmCategory = 17,
//                pfmDescription = "BANK BRI - 0026 0103 4364 503",
//                referenceNumber = "************"
//            )
//        )
    }


    override suspend fun postTransferPay(
        request: TransferPayRequest
    ): BaseResponse<TransferPayResponse> {
        return getDataWithRequest<TransferPayResponse>(
            apiService,
            brimoPrefSource,
            gson,
//            "AG6cQxkTsD4CV0z4rqUzGg==",
            "zEaUa6+4Gpe5l854uMGH2w==",
            request
        )

//        return BaseResponse(
//            code = "00",
//            description = "Sukses",
//            data = TransferPayResponse(
//                amountDataView = listOf(
//                    DataView(
//                        name = "Nominal",
//                        style = "",
//                        value = "Rp10.000"
//                    ),
//                    DataView(
//                        name = "Biaya Admin",
//                        style = "",
//                        value = "Rp0"
//                    )
//                ),
//                billingDetail = BillingDetail(
//                    description = "0026 0103 4364 503",
//                    iconName = "",
//                    iconPath = "",
//                    listType = "name",
//                    recipientAvatar = "null",
//                    subtitle = "BANK BRI",
//                    title = "RINXXXXXXXXXANI"
//                ),
//                closeButtonString = "Halaman Utama",
//                dataViewTransaction = listOf(
//                    DataView(
//                        name = "Jenis Transaksi",
//                        style = "",
//                        value = "Transfer Bank BRI"
//                    ),
//                    DataView(
//                        name = "Catatan",
//                        style = "",
//                        value = "-"
//                    )
//                ),
//                dateTransaction = "09 Juli 2025, 17:52:54 WIB",
//                footer = "",
//                footerHtml = "<html><body style=\"margin:0;padding:-16px;color:#777777;font-family:avenir\"><table><tr><td colspan=\"3\" align=\"left\" valign=\"top\" style=\"color:#777777;font-size:14px\" width=\"260\"><strong>INFORMASI:</strong></td></tr><tr><td colspan=\"3\" align=\"left\" valign=\"top\" width=\"260\"><div style=\"color:#777777;font-size:12px;line-height:18px;margin-left:-1px\"><br>Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)<br>PT. Bank Rakyat Indonesia (Persero) Tbk.<br>Kantor Pusat BRI - Jakarta Pusat<br>NPWP : 01.001.608.7-093.000</div></td></tr></table></body></html>",
//                headerDataView = listOf(
//                    DataView(
//                        name = "No. Ref",
//                        style = "",
//                        value = "************"
//                    )
//                ),
//                helpFlag = false,
//                immediatelyFlag = true,
//                onProcess = false,
//                referenceNumber = "************",
//                rowDataShow = 0,
//                share = true,
//                shareButtonString = "Bagikan Bukti Transaksi",
//                sourceAccountDataView = AccountDataView(
//                    description = "0230 **** **** 507",
//                    iconName = "",
//                    iconPath = "",
//                    listType = "name",
//                    subtitle = "BANK BRI",
//                    title = "ADIXXXXXXXXXXXXXXLTI"
//                ),
//                title = "Transaksi Berhasil",
//                titleImage = "receipt_00_revamp",
//                totalDataView = listOf(
//                    DataView(
//                        name = "Total Transaksi",
//                        style = "",
//                        value = "Rp10.000"
//                    )
//                ),
//                voucherDataView = listOf()
//            )
//        )
    }

    override suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): BaseResponse<SaldoNormalData> {
        return getDataWithRequest<SaldoNormalData>(
            apiService,
            brimoPrefSource,
            gson,
            "gnUBRK3Mc0ogiZdhmA6hBg==",
            request
        )
//        return BaseResponse(
//            code = "00",
//            description = "Sukses",
//            data = SaldoNormalData(
//                account = "***************",
//                accountString = "0230 0100 1674 308",
//                name = "ADIXXXXXXXXXXXXXXLTI",
//                currency = "Rp",
//                onHold = false,
//                balance = **********.69,
//                balanceString = "1.413.496.267,69",
//                minimumBalance = 0
//            )
//        )
    }

    override suspend fun postListCity(
        request: ListCityRequest
    ): BaseResponse<ListCityResponse> {
        return getDataWithRequest<ListCityResponse>(
            apiService,
            brimoPrefSource,
            gson,
            "LYR8nIBcEqxKo93FJpK5kS5TciKg9Pt6+fIDUwLNpUw=",
            request
        )

//        return BaseResponse(
//            code = "00",
//            description = "Sukses",
//            data = ListCityResponse(
//                listKota = listOf(
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "Kab. Polewali Mandar",
//                        sandiKota = "6401",
//                        sandiPropinsi = "6400"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "Kab. Majene",
//                        sandiKota = "6402",
//                        sandiPropinsi = "6400"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "Kab. Mamasa",
//                        sandiKota = "6403",
//                        sandiPropinsi = "6400"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KENDARI",
//                        sandiKota = "6900",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  BUTON",
//                        sandiKota = "6901",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  MUNA",
//                        sandiKota = "6903",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  WAKATOBI",
//                        sandiKota = "6905",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  KONAWE",
//                        sandiKota = "6906",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  KONAWE SELATAN",
//                        sandiKota = "6907",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  BOMBANA",
//                        sandiKota = "6908",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  BUTON UTARA",
//                        sandiKota = "6910",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  KONAWE UTARA",
//                        sandiKota = "6911",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KOTA  BAU-BAU",
//                        sandiKota = "6990",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KOTA  KENDARI",
//                        sandiKota = "6991",
//                        sandiPropinsi = "6900"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  JAYAWIJAYA",
//                        sandiKota = "8213",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  NABIRE",
//                        sandiKota = "8214",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  MIMIKA",
//                        sandiKota = "8215",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  PUNCAK JAYA",
//                        sandiKota = "8216",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  SARMI",
//                        sandiKota = "8217",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  KEEROM",
//                        sandiKota = "8218",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  PEGUNUNGAN BINTANG",
//                        sandiKota = "8221",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  YAHUKIMO",
//                        sandiKota = "8222",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  TOLIKARA",
//                        sandiKota = "8223",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  WAROPEN",
//                        sandiKota = "8224",
//                        sandiPropinsi = "8200"
//                    ),
//                    CityData(
//                        bicPeserta = "CENAIDJA",
//                        namaKota = "KAB.  BOVEN DIGOEL",
//                        sandiKota = "8226",
//                        sandiPropinsi = "8200"
//                    )
//                ),
//                adminFee = "0",
//                minimumTransaction = "100000002",
//                minimumTransactionString = "Rp100.000.002",
//                officeHourRtgs = "08:00 - 20:00",
//                referenceNumber = "323537100609"
//            )
//        )
    }

}
