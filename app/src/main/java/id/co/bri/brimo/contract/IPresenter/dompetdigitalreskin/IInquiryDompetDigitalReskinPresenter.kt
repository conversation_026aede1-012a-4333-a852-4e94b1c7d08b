package id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView

interface IInquiryDompetDigitalReskinPresenter<V : IBaseTransactionView> : IBaseTransactionPresenter<V> {

    fun setUrlConfirm(urlConfirm: String)

    fun getAccountDefault():String

    fun getSaldoRekeningUtama():String
}