package id.co.bri.brimo.ui.activities;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.android.flexbox.JustifyContent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListInboxFiturFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxPeriodeFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxStatusFilterAdapter;
import id.co.bri.brimo.adapters.ListInboxSubFilterAdapter;
import id.co.bri.brimo.contract.IPresenter.inbox.IInboxFilterPresenter;
import id.co.bri.brimo.contract.IView.inbox.IInboxFilterView;
import id.co.bri.brimo.databinding.ActivityInboxFilterBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.models.InboxStatusModel;
import id.co.bri.brimo.models.apimodel.response.ActivityGroup;
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse;
import id.co.bri.brimo.models.apimodel.response.PeriodeResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCategoryTransactionFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetSourceOfAccountFragment;

public class InboxFilterActivity extends BaseActivity implements
        ListInboxPeriodeFilterAdapter.ClickItem,
        ListInboxStatusFilterAdapter.ClickItem,
        ListInboxFiturFilterAdapter.ClickItem,
        ListInboxSubFilterAdapter.ClickItem,
        IInboxFilterView,
        View.OnClickListener {

    private ActivityInboxFilterBinding binding;

    @Inject
    IInboxFilterPresenter<IInboxFilterView> filterPresenter;

    private static final String TAG = "InboxFilterActivity";

    protected ListInboxPeriodeFilterAdapter periodeFilterAdapter;
    protected ListInboxStatusFilterAdapter statusFilterAdapter;
    protected ListInboxFiturFilterAdapter fiturFilterAdapter;
    protected ListInboxSubFilterAdapter subFilterAdapter;

    protected static FilterAktivityResponse filterAktivityResponse;

    protected List<PeriodeResponse> periodeFilterModels;
    protected List<InboxStatusModel> statusModels;
    protected ArrayList<InboxStatusModel> fiturModel;
    protected List<ActivityGroup.ActivityType> activityTypeList = null;

    private static String sPeriode = "ALL";
    private static String sStatus = "ALL";
    private static String sFitur = "ALL";
    private static String sSubFitur = "ALL";
    protected String lastId = "0";
    private static List<String> sSubFiturs = new ArrayList<>();

    private static String originalPeriode = "ALL";
    private static String originalStatus = "ALL";
    private static String originalFitur = "ALL";
    private static String originalSubFitur = "ALL";

    public static void launchIntent(ActivityResultLauncher<Intent> launcher, Activity caller, FilterAktivityResponse filterResponse,
        String periode, String status, String fitur, List<String> subFitur) {
        sPeriode = periode;
        originalPeriode = periode;
        sStatus = status;
        originalStatus = status;
        sFitur = fitur;
        originalFitur = fitur;
        sSubFiturs = subFitur != null ? new ArrayList<>(subFitur) : new ArrayList<>();
        if (!sSubFiturs.isEmpty()) {
            sSubFitur = String.join(",", sSubFiturs);
            originalSubFitur = String.join(",", sSubFiturs);
        } else {
            sSubFitur = "ALL";
            originalSubFitur = "ALL";
        }
        Intent intent = new Intent(caller, InboxFilterActivity.class);
        filterAktivityResponse = filterResponse;
        launcher.launch(intent);
    }

    public static void launchIntent(Activity caller, FilterAktivityResponse filterResponse,  String periode, String status, String fitur, List<String> subFitur) {
        Intent intent = new Intent(caller, InboxFilterActivity.class);
        sPeriode = periode;
        originalPeriode = periode;
        sStatus = status;
        originalStatus = status;
        sFitur = fitur;
        originalFitur = fitur;
        sSubFiturs = subFitur != null ? new ArrayList<>(subFitur) : new ArrayList<>();
        if (!sSubFiturs.isEmpty()) {
            sSubFitur = String.join(",", sSubFiturs);
            originalSubFitur = String.join(",", sSubFiturs);
        } else {
            sSubFitur = "ALL";
            originalSubFitur = "ALL";
        }
        filterAktivityResponse = filterResponse;
        caller.startActivityForResult(intent, Constant.REQ_FILTER_INBOX);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInboxFilterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setupToolbar();

        GeneralHelperNewSkin.INSTANCE.setToolbarRightText(this, binding.toolbar.toolbar, getString(R.string.filter), getString(R.string.reset));

        injectDependency();

        setupView();

        binding.toolbar.textRight.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
        binding.cbSubfilterAll.setOnClickListener(this);
        binding.viewCategory.setOnClickListener(this);
    }

    private void setupToolbar()  {
        GeneralHelperNewSkin.INSTANCE.setToolbarRightText(this, binding.toolbar.toolbar, getString(R.string.filter), getString(R.string.reset));
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (filterPresenter != null) {
            filterPresenter.setView(this);
            filterPresenter.start();
        }
    }

    protected String getTitleBar() {
        return GeneralHelper.getString(R.string.filter_aktivitas);
    }

    /**
     * untuk mengatur adapter
     */
    @SuppressLint("DefaultLocale")
    protected void setupView() {
        dataPeriode();

        binding.rvTimeFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        periodeFilterAdapter = new ListInboxPeriodeFilterAdapter(periodeFilterModels, this, this);
        binding.rvTimeFilter.setAdapter(periodeFilterAdapter);
        if (!Objects.equals(sPeriode, "ALL")) {
            periodeFilterAdapter.setSelectedPeriode(sPeriode);
        }

        if (!Objects.equals(sFitur, "ALL")) {
            String name;
            if (sFitur.equalsIgnoreCase("ALL")) {
                name = getString(R.string.semua);
            } else{
                name = sFitur;
            }

            if (sSubFiturs != null) {
                int count = sSubFiturs.size();
                if (count > 0) {
                    binding.tvSourceAccount.setText(String.format("%s (%d)", name, count));
                } else {
                    binding.tvSourceAccount.setText(name);
                }
            }

        }

        if (filterAktivityResponse.getActivityStatusList() != null) {
            FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(this);
            layoutManager.setFlexDirection(FlexDirection.ROW);
            layoutManager.setJustifyContent(JustifyContent.FLEX_START);
            layoutManager.setFlexWrap(FlexWrap.WRAP);
            binding.rvMainFilter.setLayoutManager(layoutManager);

            for (InboxStatusModel item : statusModels) {
                if ("All".equalsIgnoreCase(item.getName())) {
                    item.setName(getString(R.string.status_semua));
                }
            }

            List<String> order = Arrays.asList(
                    getString(R.string.status_semua),     // "Semua"
                    getString(R.string.status_sukses),    // "Sukses"
                    getString(R.string.status_gagal),     // "Gagal"
                    getString(R.string.status_menunggu),  // "Menunggu"
                    getString(R.string.status_diproses)   // "Diproses"
            );

            statusModels.sort(Comparator.comparingInt(
                    item -> order.contains(item.getName()) ? order.indexOf(item.getName()) : Integer.MAX_VALUE
            ));

            statusFilterAdapter = new ListInboxStatusFilterAdapter(statusModels, this, this);
            binding.rvMainFilter.setAdapter(statusFilterAdapter);
            if (!Objects.equals(sStatus, "ALL")) {
                statusFilterAdapter.setSelectedStatus(sStatus);
            }

            binding.rvTitleFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
            fiturFilterAdapter = new ListInboxFiturFilterAdapter(fiturModel, this, this);
            binding.rvTitleFilter.setAdapter(fiturFilterAdapter);

            binding.rvSubtitleFilter.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        }
    }

    /**
     * merubah data adapter
     */
    private void dataPeriode() {
        periodeFilterModels = new ArrayList<>();
        periodeFilterModels.add(0, new PeriodeResponse("ALL", GeneralHelper.getString(R.string.semua)));
        periodeFilterModels.add(1, new PeriodeResponse("0D", GeneralHelper.getString(R.string.today)));
        periodeFilterModels.add(2, new PeriodeResponse("1D", GeneralHelper.getString(R.string.yesterday)));
        periodeFilterModels.add(3, new PeriodeResponse("7D", GeneralHelper.getString(R.string.last_7_days)));
        periodeFilterModels.add(4, new PeriodeResponse("1W", GeneralHelper.getString(R.string.this_week)));
        periodeFilterModels.add(5, new PeriodeResponse("1M", GeneralHelper.getString(R.string.this_month)));

        if (filterAktivityResponse != null) {
            statusModels = new ArrayList<>();
            for (int i = 0; i < filterAktivityResponse.getActivityStatusList().size(); i++) {
                statusModels.add(i, new InboxStatusModel(filterAktivityResponse.getActivityStatusList().get(i).getCodeGroup(),
                        filterAktivityResponse.getActivityStatusList().get(i).getNameGroup(), false, false));
            }

            fiturModel = new ArrayList<>();
            for (int i = 0; i < filterAktivityResponse.getActivityGroupList().size(); i++) {
                fiturModel.add(i, new InboxStatusModel(filterAktivityResponse.getActivityGroupList().get(i).getCodeGroup(),
                        filterAktivityResponse.getActivityGroupList().get(i).getNameGroup(), false, false));
            }
        }
    }

    /**
     * untuk memunculkan data subfilter berdasarkan filter yang dipilih
     */
    public void subFilter() {

        activityTypeList = new ArrayList<>();
        for (int i = 0; i < filterAktivityResponse.getActivityGroupList().size(); i++) {
            for (int j = 0; j < filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().size() - 1; j++) {
                if (filterAktivityResponse.getActivityGroupList().get(i).getCodeGroup().equalsIgnoreCase(sFitur)) {
                    activityTypeList.add(j, new ActivityGroup.ActivityType(
                            filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().get(j + 1).getCodeType(),
                            filterAktivityResponse.getActivityGroupList().get(i).getActivityTypeList().get(j + 1).getName()));
                }
            }
        }
    }

    /**
     * PeriodeResponse balikan untuk waktu
     */
    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void itemClickedPeriode(PeriodeResponse periodeResponseList) {

        clearSelectedPeriode();

        periodeResponseList.setSelected(true);
        sPeriode = periodeResponseList.getCode();
        periodeFilterAdapter.notifyDataSetChanged();

        if (activityTypeList != null) {
            if ((sFitur.equalsIgnoreCase("ALL") || activityTypeList.isEmpty())) {
                if (!sPeriode.equalsIgnoreCase(originalPeriode) || !sStatus.equalsIgnoreCase(originalStatus)
                        || !sFitur.equalsIgnoreCase(originalFitur) || !sSubFitur.equalsIgnoreCase(originalSubFitur)) {
                    enableButton();
                }
            } else {
                validationButton();
            }
        } else {
            validationButton();
        }
    }

    /**
     * @param inboxStatusModel balikan untuk status
     */
    @Override
    public void itemClickedStatus(InboxStatusModel inboxStatusModel) {
        clearSelectedStatus();
        inboxStatusModel.setSelected(true);
        sStatus = inboxStatusModel.getCode();
        statusFilterAdapter.notifyDataSetChanged();

//        if (sStatus.equalsIgnoreCase("ALL"))
//            return;

        if (activityTypeList != null) {
            if ((sFitur.equalsIgnoreCase("ALL") || activityTypeList.size() == 0)) {
                enableButton();
            } else {
                validationButton();
            }
        } else {
            validationButton();
        }
    }

    /**
     * @param filterResponses balikan untuk fitur
     */
    @Override
    public void itemClickedFitur(InboxStatusModel filterResponses) {
        clearSelectedFitur();
        filterResponses.setSelected(true);
        sFitur = filterResponses.getCode();
        fiturFilterAdapter.notifyDataSetChanged();

        if (sSubFiturs != null) {
            sSubFiturs.clear();
        }

        subFilter();
        binding.rvSubtitleFilter.setVisibility(View.VISIBLE);
        subFilterAdapter = new ListInboxSubFilterAdapter(activityTypeList, this, this);
        binding.rvSubtitleFilter.setAdapter(subFilterAdapter);

        if (activityTypeList.size() == 0) {
            if (!sPeriode.equalsIgnoreCase("ALL") && !sStatus.equalsIgnoreCase("ALL"))
                enableButton();
            else
                disableButton();
        } else {
            sSubFiturs.clear();
            validationButton();
        }

        if (activityTypeList.size() == 0) {
            binding.cbSubfilterAll.setVisibility(View.GONE);
        } else {
            binding.cbSubfilterAll.setChecked(false);
            binding.cbSubfilterAll.setVisibility(View.VISIBLE);
        }

    }


    /**
     * @param filterModel balikan untuk subFitur
     */
    @Override
    public void onClickedSubFilterItem(ActivityGroup.ActivityType filterModel) {
        if (filterModel.isSelected()) {
            sSubFiturs.add(filterModel.getCodeType());

            if (activityTypeList.size() == sSubFiturs.size()) {
                binding.cbSubfilterAll.setChecked(true);
            }

        } else {
            for (int i = 0; i < sSubFiturs.size(); i++) {
                String codeType = filterModel.getCodeType();
                String subFilter = sSubFiturs.get(i);
                if (codeType != null && codeType.equalsIgnoreCase(subFilter))
                    if (!filterModel.isSelected())
                        sSubFiturs.remove(i);

                if (activityTypeList.size() != sSubFiturs.size()) {
                    binding.cbSubfilterAll.setChecked(false);
                }
            }
        }

        validationButton();

    }

    public void validationButton() {

        if (!sPeriode.equalsIgnoreCase(originalPeriode)
                || !sStatus.equalsIgnoreCase(originalStatus)
                || !sFitur.equalsIgnoreCase(originalFitur)
                || !sSubFitur.equalsIgnoreCase(originalSubFitur)) {
            enableButton();
        } else {
            disableButton();
        }
    }


    public void enableButton() {
        binding.btnSubmit.setEnabled(true);
    }

    public void disableButton() {
        binding.btnSubmit.setEnabled(false);
    }

    @SuppressLint("DefaultLocale")
    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.textRight:
                clearAll();
                break;
            case R.id.btnSubmit:
                if (sSubFiturs != null) {
                    sSubFitur = String.join(",", sSubFiturs);
                }
                Intent intent = new Intent();
                intent.putExtra("PERIODE", sPeriode);
                intent.putExtra("STATUS", sStatus);
                intent.putExtra("FITUR", sFitur);
                intent.putExtra("SUBFITUR", sSubFitur);
                if (sSubFiturs != null) {
                    intent.putStringArrayListExtra("SUBFITURLIST", new ArrayList<>(sSubFiturs));
                }
                intent.putExtra("LASTID", lastId);
                setResult(RESULT_OK, intent);
                finish();
                break;
            case R.id.cb_subfilter_all:
                if (binding.cbSubfilterAll.isChecked()) {
                    subFilterAdapter.setCbSubAll(true);
                } else {
                    subFilterAdapter.setCbSubAll(false);
                }
                if (sSubFiturs != null) {
                    sSubFiturs.clear();
                }
                subFilterAdapter.notifyDataSetChanged();
                break;
            case R.id.view_category:
                BottomSheetCategoryTransactionFragment categoryTransactionFragment = BottomSheetCategoryTransactionFragment.Companion.newInstance(fiturModel, filterAktivityResponse, sFitur, sSubFiturs);
                categoryTransactionFragment.setOnSelectedListener((item, feature, subFeature, subFeatureList, activityTypes) -> {
                    String name = item.getName() != null ? item.getName() : "ALL";
                    int count = subFeatureList.size();
                    if (count > 0) {
                        binding.tvSourceAccount.setText(String.format("%s (%d)", name, count));
                    } else {
                        if (name.equals("All") || name.equals(getString(R.string.all))) {
                            binding.tvSourceAccount.setText(getString(R.string.txt_all_transaction_category));
                        } else {
                            binding.tvSourceAccount.setText(name);
                        }
                    }
                    sFitur = feature;
                    if (activityTypes.isEmpty()) {
                        sSubFitur = "ALL";
                        originalSubFitur = "ALL";
                    } else {
                        sSubFitur = subFeature;
                    }
                    sSubFiturs = subFeatureList;
                    activityTypeList = (List<ActivityGroup.ActivityType>) activityTypes;
                    if (activityTypes.isEmpty() && !sPeriode.equalsIgnoreCase(originalPeriode)) {
                        enableButton();
                    } else {
                        validationButton();
                    }
                });

                categoryTransactionFragment.show(getSupportFragmentManager(), Constant.BOTTOMSHEET_SOURCE_OF_ACCOUNT);
                break;
        }
    }

    private void clearAll() {

        clearSelectedPeriode();
        clearSelectedStatus();
        clearSelectedFitur();
        periodeFilterAdapter.setDefaultValue();
        statusFilterAdapter.setDefaultValue();
        if (sSubFiturs != null) {
            sSubFiturs.clear();
        }
        binding.cbSubfilterAll.setVisibility(View.GONE);
        sSubFitur = "ALL";
        binding.rvSubtitleFilter.setVisibility(View.GONE);
        binding.tvSourceAccount.setText(getString(R.string.txt_all_transaction_category));

        if (!Objects.equals(originalPeriode, "ALL") || !Objects.equals(originalStatus, "ALL") || !Objects.equals(originalFitur, "ALL")) {
            enableButton();
        } else {
            disableButton();
        }

    }

    private void clearSelectedPeriode() {
        sPeriode = "ALL";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            periodeFilterModels.forEach((p) -> p.setSelected(false));
        } else {
            for (PeriodeResponse tr : periodeFilterModels) {
                tr.setSelected(false);
            }
        }
        periodeFilterAdapter.notifyDataSetChanged();
    }

    private void clearSelectedStatus() {
        sStatus = "ALL";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            statusModels.forEach((p) -> p.setSelected(false));
        } else {
            for (InboxStatusModel sm : statusModels) {
                sm.setSelected(false);
            }
        }
        statusFilterAdapter.notifyDataSetChanged();
    }

    private void clearSelectedFitur() {
        sFitur = "ALL";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            fiturModel.forEach((p) -> p.setSelected(false));
        } else {
            for (InboxStatusModel tr : fiturModel) {
                tr.setSelected(false);
            }
        }
        fiturFilterAdapter.notifyDataSetChanged();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}