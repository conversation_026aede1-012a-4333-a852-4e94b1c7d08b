package id.co.bri.brimo.ui.activities.inforekeningnewskin

import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.avaya.ocs.Services.Work.Schema.Resource
import com.google.android.material.textfield.TextInputLayout
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.IEditAliasPresenter
import id.co.bri.brimo.contract.IView.IEditAliasView
import id.co.bri.brimo.databinding.ActivityEditAliasNameBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import javax.inject.Inject

class EditAliasNameActivity : NewSkinBaseActivity(), IEditAliasView {

    @Inject
    lateinit var editAliasPresenter: IEditAliasPresenter<IEditAliasView>

    private lateinit var binding: ActivityEditAliasNameBinding
    var resultIntent: Intent = Intent()
    var mAccount: String? = null
    var mAlias: String? = null
    private var textAlias: String? = null
    var isInitialSet = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditAliasNameBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        injectDependency()
        setupView()
        validateAlias()
    }

    private fun setupToolbar()  {
        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.edit_nickname)
        )
        binding.content.bringToFront()
    }

    private fun setupView() {

        binding.etNamaAlias.setTypeface(binding.etNamaAlias.typeface, Typeface.BOLD)
        binding.inputAlias.typeface = ResourcesCompat.getFont(this, R.font.bri_digital_text_regular)


        mAccount = intent.getStringExtra(Constant.ACCOUNT)
        mAlias = intent.getStringExtra(Constant.USER_ALIAS)

        val cleanAlias = mAlias
            ?.takeIf { it.isNotBlank() && it != getString(R.string.hint_no_alias) }
            ?.replaceFirst("@", "")
            ?.trim()

        isInitialSet = true
        if (!mAlias.isNullOrEmpty() || mAlias != getString(R.string.hint_no_alias)) {
            mAlias?.let {
//                val aliasText = it.trim().let { trimmed ->
//                    if (trimmed.startsWith("@")) trimmed else "@$trimmed"
//                }
                binding.etNamaAlias.setText(mAlias)
            }
        } else {
            binding.etNamaAlias.setText(cleanAlias)
        }

//        binding.etNamaAlias.setOnFocusChangeListener { _, hasFocus ->
//            if (hasFocus) {
//                // Jika mAlias kosong/null, kosongkan field
//                if (mAlias.isNullOrEmpty()) {
//                    binding.etNamaAlias.setText("")
//                } else {
//                    mAlias?.let {
//                        binding.etNamaAlias.setText(mAlias)
//                    }
//                }
//            }
//        }
//
//        binding.inputAlias.setEndIconOnClickListener {
//            binding.etNamaAlias.setText("")
//            mAlias = null
//        }

        mAlias = cleanAlias

    }

    private fun injectDependency() {
        getActivityComponent().inject(this)
    }

    override fun onResume() {
        super.onResume()
        editAliasPresenter.view = this
        editAliasPresenter.start()
    }

    private fun validateAlias() {

        binding.etNamaAlias.addTextChangedListener(object : TextWatcher {
            var isEditing = false

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                if (isEditing) return
                isEditing = true

                if (TextUtils.isEmpty(s)) {
                    binding.inputAlias.endIconDrawable = null
                } else {
                    if (s?.length == 1) {
                        binding.inputAlias.endIconDrawable = null
                    } else
                        binding.inputAlias.endIconDrawable = ContextCompat.getDrawable(
                            this@EditAliasNameActivity,
                            R.drawable.ic_clear_input_ns
                        )
                    }

                val original = s.toString()

                var updated = original

                if (!updated.startsWith("@")) {
                    updated = "@$updated"
                }
                if (updated != original) {
                    binding.etNamaAlias.setText(updated)
                    binding.etNamaAlias.setSelection(updated.length)
                }

                // Jika initial setText, skip validasi
                if (isInitialSet) {
                    isInitialSet = false
                    isEditing = false
                    return
                }

                val aliasOnly = updated.removePrefix("@")
                val isSameAsCurrent = aliasOnly == mAlias
                val isTooShort = aliasOnly.length < 5
                val isEmpty = aliasOnly.isEmpty()
                val isValidRegex = ValidationHelper.isUserAlias(s.toString())

                val isValid = !isEmpty && !isTooShort && !isSameAsCurrent && isValidRegex
                binding.btnSave.isEnabled = isValid

                when {
                    isEmpty || isTooShort -> {
                        binding.inputAlias.isSelected = true
                        binding.tvAliasDesc.setText(R.string.text_min_5_char)
                        binding.tvAliasDesc.setTextColor(ContextCompat.getColor(this@EditAliasNameActivity, R.color.ns_red))
                    }
                    isSameAsCurrent -> {
                        binding.inputAlias.isSelected = true
                        binding.tvAliasDesc.setText(R.string.txt_alias_already_use)
                        binding.tvAliasDesc.setTextColor(ContextCompat.getColor(this@EditAliasNameActivity, R.color.ns_red))
                    }
                    else -> {
                        binding.inputAlias.isSelected = false
                        binding.tvAliasDesc.setText(R.string.minimum_five_char)
                        binding.tvAliasDesc.setTextColor(ContextCompat.getColor(this@EditAliasNameActivity, R.color.black_ns_600))
                    }
                }

                isEditing = false
            }
        })

        binding.btnSave.setOnClickListener {
            onSubmit()
        }

    }

    override fun onSuccessGetUpdateTitle(title: String?) {
        val newAlias = binding.etNamaAlias.text.toString().replace("@", "")
        resultIntent.putExtra(Constant.TAG_ALIAS, newAlias)
        resultIntent.putExtra(Constant.TAG_POSITION, mAccount)
        setResult(RESULT_OK, resultIntent)
        finish()
    }

    override fun getTextAlias(): String? {
        textAlias = ValidationHelper.cleanSpecialChar(binding.etNamaAlias.getText().toString())
        return textAlias
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.nickname_changed_unsuccessfully_ns), SnackBarType.ERROR)
    }

    fun onSubmit() {
        val s = binding.etNamaAlias.getText().toString()
        val newAlias = s.replace("@", "")
        val url = GeneralHelper.getString(R.string.url_update_alias)
        editAliasPresenter.setUpdateAlias(url, newAlias, mAccount)
    }

    override fun onPause() {
        super.onPause()
        GeneralHelperNewSkin.hideKeyboardIfVisible(this)
    }

    companion object {

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            alias: String?,
            account: String?
        ) {

            val intent = Intent(caller, EditAliasNameActivity::class.java).also {
                it.putExtra(Constant.USER_ALIAS, alias)
                it.putExtra(Constant.ACCOUNT, account)
            }
            caller.startActivityForResult(intent, Constant.REQ_EDIT_SAVED)
        }
    }
}