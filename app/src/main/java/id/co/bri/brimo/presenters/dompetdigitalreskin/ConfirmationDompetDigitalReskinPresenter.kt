package id.co.bri.brimo.presenters.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IConfirmationDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IConfirmationDompetDigitalReskinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.DbConfig
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.ConfirmationDompetDigitalRevRequest
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.FastConfirmationDompetDigitalRevRequest
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.FastPaymentDompetDigitalRevRequest
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.PaymentDompetDigitalRevRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.daomodel.Transaksi
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver

class ConfirmationDompetDigitalReskinPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IConfirmationDompetDigitalReskinPresenter<V> where V : IMvpView?, V : IConfirmationDompetDigitalReskinView? {
    private lateinit var mUrlConfirm: String
    private lateinit var mUrlPayment: String
    private lateinit var confirmationRequest: Any
    protected var idPayment: Long = 0
    protected var paymentRequest: Any? = null
    private var isGeneral: Boolean = false
    private var disposablePayment: Disposable? = null

    override fun getDataPayment(
        pin: String,
        generalConfirmationResponse: GeneralConfirmationResponse,
        fromFast: Boolean,
        isUsingC2: Boolean
    ) {
        if (mUrlPayment == null) {
            return
        }

        if (!isViewAttached) {
            return
        }

        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber

            paymentRequest = if (fromFast) FastPaymentDompetDigitalRevRequest(
                getFastMenuRequest(),
                generalConfirmationResponse.referenceNumber,
                generalConfirmationResponse.pfmCategory.toString(), pin, ""
            )
            else PaymentDompetDigitalRevRequest(
                generalConfirmationResponse.referenceNumber,
                generalConfirmationResponse.pfmCategory.toString(), pin, ""
            )

            disposablePayment = generateRevampPaymentDisposable(
                generalConfirmationResponse,
                mUrlPayment,
                paymentRequest,
                seqNum
            )
            compositeDisposable.add(disposablePayment!!)
        }
    }

    override fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(
                transaksiPfmSource
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : DisposableSingleObserver<Long?>() {
                        override fun onSuccess(aLong: Long) {
                        }

                        override fun onError(e: Throwable) {
                            // do nothing
                        }
                    })
            )
        }
    }

    override fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String?,
        billingName: String?
    ): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(
                kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                referenceNumber!!.toLong(),
                idPayment
            )
        } catch (e: java.lang.Exception) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi
    }

    override fun isGeneral(general: Boolean) {
        isGeneral = general
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

    override fun getDataConfirmation(
        refNum: String,
        accountNum: String,
        amount: String,
        save: String,
        note: String,
        fromFast: Boolean
    ) {
        if (mUrlConfirm.isEmpty() || !isViewAttached) return

        confirmationRequest = if (fromFast) {
            FastConfirmationDompetDigitalRevRequest(
                getFastMenuRequest(),
                refNum,
                accountNum,
                amount,
                save,
                note
            )
        } else {
            ConfirmationDompetDigitalRevRequest(
                refNum,
                accountNum,
                amount,
                save,
                note
            )
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getData(mUrlConfirm, confirmationRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val generalConfirmationResponse = response.getData(
                            GeneralConfirmationResponse::class.java
                        )
                        getView()?.onGetDataConfirmation(generalConfirmationResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                            getView()?.onExceptionTrxExpired(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        compositeDisposable.add(disposable)
    }

    fun generateRevampPaymentDisposable(
        generalConfirmationResponse: GeneralConfirmationResponse,
        urlPayment: String?,
        paymentRequest: Any?,
        seqNum: String?
    ): Disposable {
        val disposable: Disposable =
            apiSource.getData(urlPayment, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()

                        val receiptRevampResponse = response.getData(
                            ReceiptRevampResponse::class.java
                        )

                        getView()!!.onSuccessGetPayment(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onExceptionTrxExpired(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_01.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException01(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value) getView()!!.onExceptionLimitExceed(
                            restResponse.getData(
                                GeneralResponse::class.java
                            )
                        )
                        else if (restResponse.code == "MR") getView()!!.onExceptionRevamp("")
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_02.value,
                                ignoreCase = true
                            )
                        ) {
                            val exception02 = restResponse.getData(onExceptionWH::class.java)
                            getView()!!.onExceptionWH(exception02)
                        } else getView()!!.onException(restResponse.desc)
                    }
                })
        return disposable
    }


    override fun start() {
        super.start()
        setDisablePopup(true)
    }

    override fun stop() {
        super.stop()
    }
}