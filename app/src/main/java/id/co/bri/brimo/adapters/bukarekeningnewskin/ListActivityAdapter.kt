package id.co.bri.brimo.adapters.bukarekeningnewskin

import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemInboxNewskinBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.apimodel.response.InboxResponse.ActivityList

class ListActivityAdapter(
    private var transaksiInboxResponses: List<ActivityList?>?,
    private val mActivity: Activity?,
    private val onClick: OnClickItem?,
    private val onSelect: OnSelected?
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    val TAG: String = "ListInboxAdapter"
    val colorArray: IntArray = intArrayOf(
        R.color.colorStatusBerhasil,
        R.color.colorStatusGagal,
        R.color.colorStatusPending
    )
    private var lastPosition = -1
    val ITEM: Int = 0
    val LOADING: Int = 1

    private var imageInbox: String? = null

    interface OnClickItem {
        fun onClickDetail(position: Int)
    }

    interface OnSelected {
        fun onSelectedItemId(position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == ITEM) {
            InboxHolder(
                ItemInboxNewskinBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        } else {
            LoadingHolder(
                ItemInboxNewskinBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val transaksiInbox = transaksiInboxResponses!![position]
        val mItemViewContext = holder.itemView.context
        if (holder is InboxHolder) {
            val inboxVH = holder

            if (position == (itemCount - 1)) {
                if (itemCount >= 10) {
                    onSelect!!.onSelectedItemId(position)
                }
            }

            val originalDetail = transaksiInbox?.subtitle
            val detail = originalDetail?.substringBefore("\n")
            val amount = originalDetail?.substringAfter("\n")

            inboxVH.binding.tvTransaksi.setText(transaksiInbox!!.title)
            inboxVH.binding.tvDetailTransaksi.setText(detail)

            val original = transaksiInbox.date
            val timeWithSpace = original.takeLast(6)
            val datePart = original.dropLast(6).trimEnd()
            val date = "$datePart,$timeWithSpace"

            inboxVH.binding.tvTanggalTransaksi.setText(date)
            inboxVH.binding.tvStatusInbox.setText(transaksiInbox.status)
            inboxVH.binding.tvNominal.setText(amount)

            if (transaksiInbox.trxStatus.equals("Success")) {
                inboxVH.binding.tvStatusInbox.background = inboxVH.itemView.resources.getDrawable(R.drawable.rounded_chip_success)
                inboxVH.binding.tvStatusInbox.setTextColor(inboxVH.itemView.resources.getColor(R.color.green_ns_600))
            } else if (transaksiInbox.trxStatus.equals("In Progress")) {
                inboxVH.binding.tvStatusInbox.background = inboxVH.itemView.resources.getDrawable(R.drawable.rounded_chip_in_progress)
                inboxVH.binding.tvStatusInbox.setTextColor(inboxVH.itemView.resources.getColor(R.color.yellow_ns_700))
            } else {
                inboxVH.binding.tvStatusInbox.background = inboxVH.itemView.resources.getDrawable(R.drawable.rounded_chip_fail)
                inboxVH.binding.tvStatusInbox.setTextColor(inboxVH.itemView.resources.getColor(R.color.red_ns_600))
            }

            if (transaksiInbox.iconName != null) {
                try {
                    val subStringIcon = transaksiInbox.iconName.substring(5)
                    if (!subStringIcon.isEmpty()) {
                        imageInbox = "ic_menu_qna_$subStringIcon"
                    }
                } catch (ignored: Exception) {
                }
            }
            GeneralHelper.loadIconTransaction(
                mActivity,
                transaksiInbox.iconPath,
                imageInbox,
                inboxVH.binding.ivIconInbox,
                GeneralHelper.getImageId(mActivity, "bri")
            )

            if (transaksiInbox.trxStatus != null) {
                if (transaksiInbox.trxStatus.equals("Success", ignoreCase = true)) {
                    inboxVH.binding.tvTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            R.color.black3
                        )
                    )
                    inboxVH.binding.tvDetailTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            R.color.ns_black500
                        )
                    )
                } else if (transaksiInbox.trxStatus.equals("Failed", ignoreCase = true)) {
                    inboxVH.binding.tvTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            colorArray[1]
                        )
                    )
                    inboxVH.binding.tvDetailTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            colorArray[1]
                        )
                    )
                } else {
                    inboxVH.binding.tvTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            R.color.black3
                        )
                    )
                    inboxVH.binding.tvDetailTransaksi.setTextColor(
                        ContextCompat.getColor(
                            mItemViewContext,
                            R.color.ns_black500
                        )
                    )
                }
            }

            holder.itemView.setOnClickListener { view: View? ->
                if (!transaksiInbox.trxStatus.equals("Failed", ignoreCase = true)) {
                    onClick?.onClickDetail(position)
                }
            }

            transaksiInboxResponses?.let { list ->
                if (position == list.size - 1) {
                    inboxVH.binding.viewLine.makeGone()
                } else {
                    inboxVH.binding.viewLine.makeVisible()
                }
            }

        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (transaksiInboxResponses!![position] == null) LOADING else ITEM
    }

    fun setItems(activityLists: List<ActivityList?>?) {
        transaksiInboxResponses = activityLists
    }
    override fun getItemCount(): Int {
        return if (transaksiInboxResponses == null) 0 else transaksiInboxResponses!!.size
    }
    class InboxHolder(binding: ItemInboxNewskinBinding) : RecyclerView.ViewHolder(binding.getRoot()) {
        var binding: ItemInboxNewskinBinding = binding
    }
    class LoadingHolder(binding: ItemInboxNewskinBinding) : RecyclerView.ViewHolder(binding.getRoot()) {
        var binding: ItemInboxNewskinBinding = binding
    }
}
