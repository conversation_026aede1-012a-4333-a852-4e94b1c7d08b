package id.co.bri.brimo.contract.IPresenter.dompetdigitalrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IInputNomorPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getDataInquiry(eWalletCode: String, corpCode: String, purchaseNumber: String)

    fun setUrlInquiry(url: String)

    fun setUrlConfirm(url: String)

    fun setUrlPayment(url: String)
}