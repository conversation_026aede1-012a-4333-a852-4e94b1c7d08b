package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import id.co.bri.brimo.models.Office;

import java.util.ArrayList;

public class ListCabangResponse {

    @SerializedName("office")
    @Expose
    private ArrayList<Office> officeList;
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;

    public ListCabangResponse(ArrayList<Office> officeList) {
        this.officeList = officeList;
    }

    public ArrayList<Office> getOfficeList() {
        return officeList;
    }

    public void setOfficeList(ArrayList<Office> officeList) {
        this.officeList = officeList;
    }

}
