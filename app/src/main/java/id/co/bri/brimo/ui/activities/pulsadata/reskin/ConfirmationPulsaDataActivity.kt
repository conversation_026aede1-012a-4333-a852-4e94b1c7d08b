package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.transition.Fade
import androidx.transition.Slide
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountListEntity
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.databinding.ActivityOpenBillConfirmationReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.request.PaymentRevampOpenRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.presenters.pulsarevamp.reskin.PaymentNS
import id.co.bri.brimo.ui.activities.base.BaseTransactionActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.transaction_process.TransactionProcessActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.pin.reskin.PinReskinFragment
import javax.inject.Inject

class ConfirmationPulsaDataActivity: BaseTransactionActivity<ActivityOpenBillConfirmationReskinBinding>(
    ActivityOpenBillConfirmationReskinBinding::inflate),
    PinReskinFragment.SendPin,
//    PinFragment.SendPin,
    IPulsaDataReskinView {
    private var isCompletedPin = false

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    companion object {
        const val TAG = "ConfirmationPulsaDataActivity"
        private var dataConfirm: GeneralConfirmationResponse?= null
        private var dataSaved: MutableList<SavedResponse> = mutableListOf()

        private var dataAccount: AccountModel?= null

        private const val ANIMATE_DUR: Long = 300

        @JvmStatic
        fun launchIntent(
            caller: Activity, fromFastMenu: Boolean,
            response: GeneralConfirmationResponse, sumberDana: AccountModel,
            savedList: MutableList<SavedResponse>
        ) {
            dataConfirm = response
            dataAccount = sumberDana
            isFromFastMenu = fromFastMenu
            dataSaved = savedList

            Intent(caller, ConfirmationPulsaDataActivity::class.java).let { intent ->
                caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@ConfirmationPulsaDataActivity
            start()
        }
    }

    override fun onBindView() {
        GeneralHelper.setToolbarNs(this, binding.toolbar, "Konfirmasi")

        binding.btnLihatLebih.visibility = View.GONE
        binding.btnSubmit.setOnItemClickListener {
            val pinFragment = PinReskinFragment(this@ConfirmationPulsaDataActivity, this)
            pinFragment.show()
        }
        binding.switchSave.setOnCheckedChangeListener { _, isChecked ->
            val transitionSet: TransitionSet = TransitionSet()
                .addTransition(Fade())
                .addTransition(Slide(Gravity.LEFT))
                .setDuration(ANIMATE_DUR)

            TransitionManager.beginDelayedTransition(binding.bivSaveName.parent as ViewGroup, transitionSet)

            if(isChecked) {
                binding.bivSaveName.visibility = View.VISIBLE
            } else {
                binding.bivSaveName.visibility = View.GONE
            }
        }

        initViews()
    }

    private fun initViews() {
        val billingDetail = dataConfirm!!.billingDetail
        val mSaved = dataSaved.find { it.subtitle == billingDetail.description }
        binding.rlFavorit.visibility = if(mSaved!=null || isFromFastMenu) View.GONE else View.VISIBLE
        if(!isFromFastMenu) binding.llSavedAs.visibility = if(mSaved!=null) View.VISIBLE else View.GONE
        binding.tvSavedAs.text = String.format(
            GeneralHelper.getString(R.string.txt_tersimpan_sebagai),
            mSaved?.title
        )

        GeneralHelper.loadIconTransaction(
            this,
            billingDetail.iconPath,
            billingDetail.iconName.split("\\.".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
            binding.ivArea,
            GeneralHelper.getImageId(this, "bri")
        )

        binding.tvNameCust.text = billingDetail.subtitle
        binding.tvNumberCust.text = String.format(
            GeneralHelper.getString(R.string.transaction_detail_content),
            billingDetail.title,
            billingDetail.description
        )

        binding.tvNominal.text = dataConfirm!!.amountString
        binding.tvAdminFee.text = dataConfirm!!.adminFeeString

        binding.bivSaveName.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                clearText()
            }
        }

        binding.btnSubmit.setAmountText(dataConfirm!!.payAmountString)
        binding.totalTagihan.text = dataConfirm!!.payAmountString

        dataAccount?.let {
            binding.tvNumberAccount.text = it.name
            binding.tvNominalAccount.text = it.acoountString

            GeneralHelper.loadIconTransaction(
                this,
                it.imagePath,
                it.imageName,
                binding.ivRekening,
                R.drawable.img_card_bg)
        }
    }

    override fun onSendPinComplete(pin: String) {
        isCompletedPin = true

        presenter.payment(
            PaymentNS(
                dataConfirm!!.referenceNumber,
                pin!!,
                dataAccount?.acoount!!,
                dataConfirm!!.pfmCategory.toString(),
                binding.bivSaveName.getText()
            )
        )
    }

    override fun onLupaPin() {
    }

    fun mParameterModel(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal)
        parameterModel.defaultIcon = R.drawable.ic_default_pulsa
        return parameterModel
    }

    override fun onSuccessGetData(formPulsaDataResponse: FormPulsaDataResponse) {
    }

    override fun onSuccessGetDataConfirmation(response: GeneralConfirmationResponse) {
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {
        finish()
        dataAccount?.let {
            TransactionProcessActivity.launchIntent(this,
                isFromFastMenu, receiptRevampResponse, it, ReceiptType.OTHER
            )
        }
    }

    override fun onException12(message: String) {
    }

    override fun onException93(message: String) {
    }

    override fun onExceptionGetDataForm(message: String) {
    }

    override fun fromFastmenu(): Boolean {
        return isFromFastMenu
    }

    override fun onSuccess(
        data: RestResponse,
        type: FavoriteType
    ) {

    }
}