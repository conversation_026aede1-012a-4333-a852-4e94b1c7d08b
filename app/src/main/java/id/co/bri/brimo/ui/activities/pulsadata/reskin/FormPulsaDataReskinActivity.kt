package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.os.Bundle
import android.provider.ContactsContract
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import androidx.appcompat.widget.SearchView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountListEntity
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.databinding.ActivityFormPulsaDataReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FavoriteEvent
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.util.RxBus
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import io.reactivex.android.schedulers.AndroidSchedulers
import javax.inject.Inject

class FormPulsaDataReskinActivity: NewSkinBaseActivity(), IPulsaDataReskinView, SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem {
    private var _binding: ActivityFormPulsaDataReskinBinding? = null
    protected val binding get() = _binding!!

    val providerList: MutableList<ProviderItem> = mutableListOf()
    private var isProviderFound: Boolean = false
    private lateinit var currentProvider: ProviderItem

    private lateinit var dataFormPulsa: FormPulsaDataResponse

    // Custom hint
    private lateinit var customHint : SpannableString

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    lateinit var historyAdapter: HistoryAdapterNs
    lateinit var savedAdapter: SavedAdapterNs

    protected var historyResponses: ArrayList<HistoryResponse> = ArrayList<HistoryResponse>()
    protected var savedResponses: ArrayList<SavedResponse> = ArrayList<SavedResponse>()

    private lateinit var numpadHelper: CustomNumpadHelper

    // Current tab
    private var currentTab = TAB_FAVORIT

    var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            beforeText()
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            afterText(editable)
            updateHintBasedOnState()

            binding.bivNoPelanggan.removeAllEndIcons()
            if (editable.toString().isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.bivNoPelanggan.clearText()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
        }
    }

    private fun updateHintBasedOnState() {
        val isExpanded = binding.bivNoPelanggan.hasFocus() || binding.bivNoPelanggan.getText().isNotEmpty()
        if (isExpanded) {
            binding.bivNoPelanggan.setHint(getString(R.string.hint_label_no_hp))
        } else {
            binding.bivNoPelanggan.setHint(customHint)
        }
    }

    companion object {
        const val TAG = "FormPulsaDataReskinActivity"

        const val TAB_FAVORIT = 0
        const val TAB_RIWAYAT = 1

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(
                    Intent(
                    this,
                    FormPulsaDataReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityFormPulsaDataReskinBinding.inflate(layoutInflater)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        RxBus.listen(FavoriteEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isAddFavorite) {
                    showSnackbar("Daftar Favorit berhasil ditambahkan.", ALERT_CONFIRM)
                    presenter.getDataForm()
                }
            }
    }

    private fun onBindView() {
        binding.llContentMain.visibility = if(!isFromFastMenu) View.VISIBLE else View.GONE
        binding.btnSubmit.visibility = if(!isFromFastMenu) View.VISIBLE else View.GONE

        binding.bivNoPelanggan.attachNumpad(this, NumpadType.PHONE, { pin ->

        }, onFocusChanged = { hasFocus ->
            updateHintBasedOnState()
        }, onAttached = { numpad ->
            numpadHelper = numpad
        })
        binding.bslContent.apply {
            setTextToolbar(this@FormPulsaDataReskinActivity, "Pulsa & Paket Data")
        }
        binding.bivNoPelanggan.apply {
            setDistancePrefix(resources.getDimensionPixelSize(R.dimen.size_8dp))
            setInputType(InputType.TYPE_CLASS_NUMBER)
            setHint(customHint)
            addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
            addTextChangedListener(activityTextListener)
        }
        binding.btnSubmit.setOnClickListener {
            if(!isProviderFound) {
                binding.bivNoPelanggan.setError("Nomor tidak ditemukan. Coba cek lagi nomor kamu.")
            } else {
                InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                    binding.bivNoPelanggan.getText(),
                    currentProvider, dataFormPulsa.referenceNumber,
                    if(isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
                ),savedResponses.toMutableList())
            }
        }

        setupAdapters()
        setupTabFunctionality()
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@FormPulsaDataReskinActivity
            getDataForm()
            start()
        }
    }

    private fun onBindIntentData() {
        customHint = customPrefixHint(
            GeneralHelper.getString(R.string.hint_prefix_62),
            GeneralHelper.getString(R.string.hint_label_no_hp)
        )
    }

    protected fun customPrefixHint (prefix: String, label: String): SpannableString {
        val spannable = SpannableString("$prefix  $label")
        spannable.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_disabled_default_ns)),
            0,
            prefix.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannable.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, if (isFromFastMenu) R.color.text_disabled_default_ns else R.color.text_black_default_ns)),
            prefix.length,
            spannable.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannable
    }

    override fun onSuccessGetData(formPulsaDataResponse: FormPulsaDataResponse) {
        historyResponses.apply {
            clear()
            addAll(formPulsaDataResponse.history)
        }
        savedResponses.apply {
            clear()
            addAll(formPulsaDataResponse.saved)
        }

        dataFormPulsa = formPulsaDataResponse
        providerList.addAll(dataFormPulsa.provider)

        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        updateEmptyStates()
    }

    override fun onSuccessGetDataConfirmation(response: GeneralConfirmationResponse) {
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {

    }

    override fun onException12(message: String) {
    }

    override fun onException93(message: String) {
    }

    override fun onExceptionGetDataForm(message: String) {
    }

    override fun fromFastmenu(): Boolean = isFromFastMenu

    override fun onSuccess(
        data: RestResponse,
        type: FavoriteType
    ) {
        presenter.getDataForm()

        showSnackbar(when (type) {
            FavoriteType.favorite -> "Daftar berhasil di Pin."
            FavoriteType.removeFavorite -> "Daftar Favorit berhasil dihapus."
            FavoriteType.unfavorite -> "Daftar Favorit berhasil diunpin."
            else -> ""
        }, ALERT_CONFIRM)
    }

    override fun onSuccessAccountList(accountList: MutableList<AccountModel>, mainAccount: AccountModel) {

    }

    private fun validatePhoneNumberAndUpdateButton() {
        val minLength = 9
        val maxLength = 12

        val inputLength = binding.bivNoPelanggan.getText().length

        // Handle phone number validation errors
        if (inputLength > 0) {
            if (inputLength < minLength) {
                binding.bivNoPelanggan.setError(
                    String.format(
                        GeneralHelper.getString(R.string.phone_minimal_input),
                        (minLength + 1).toString()
                    )
                )
            } else if (inputLength > maxLength) {
                binding.bivNoPelanggan.setError(
                    String.format(
                        GeneralHelper.getString(R.string.phone_maksimal_input),
                        (maxLength + 1).toString()
                    )
                )
            } else {
                binding.bivNoPelanggan.clearError()
            }
        } else {
            binding.bivNoPelanggan.clearError()
        }

        // Update button state based on both wallet selection and phone number validity
        updateSubmitButtonState()
    }

    private fun updateSubmitButtonState() {
        val minLength = 9
        val maxLength = 12
        val inputLength = binding.bivNoPelanggan.getText().length

        val isPhoneNumberValid = inputLength in minLength..maxLength

        binding.btnSubmit.isEnabled = isPhoneNumberValid
    }

    override fun changeText(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
        super.changeText(charSequence, i, i1, i2)

        val input = charSequence.toString()
        val normalized = normalizePhoneNumber(input)

        validatePhoneNumberAndUpdateButton()

        // Hanya update jika input berubah setelah normalisasi
        if (input != normalized) {
            binding.bivNoPelanggan.removeTextChangedListener(activityTextListener)
            binding.bivNoPelanggan.setText(normalized)
            binding.bivNoPelanggan.setSelection(normalized.length)
            binding.bivNoPelanggan.addTextChangedListener(activityTextListener)

            binding.bivNoPelanggan.removeAllEndIcons()
            if (input.isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.bivNoPelanggan.clearText()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
        }

        when {
            (charSequence.length >= 3 && charSequence.length < 5) -> {
                if (!isProviderFound) {
                    showingToForm("0$normalized")
                }
            }

            (charSequence.length == 5 || (charSequence.length >= 5 && !isProviderFound)) -> {
                showingToForm("0$normalized")
            }

            charSequence.length < 4 -> {
                binding.bivNoPelanggan.setStartIconWithUrl("")
            }
        }
    }

    fun prefix(phone: String) {
        var prefixInput = phone.substring(0, 4)
        var prefixInputCustom = ""
        if (phone.length >= 5) {
            prefixInputCustom = phone.substring(0, 5)
        }

        for (p in providerList) {
            if ((prefixInputCustom.isNotEmpty() && GeneralHelper.isContains(
                    p.prefix,
                    prefixInputCustom
                )) || GeneralHelper.isContains(p.prefix, prefixInput)
            ) {
                currentProvider = p
                binding.bivNoPelanggan.setStartIconWithUrl(p.iconPath)
                isProviderFound = true

                break
            }
        }
    }

    private fun showingToForm(number: String) {
        findProviderByPrefix(number)?.let {
            currentProvider = it
            binding.bivNoPelanggan.setStartIconWithUrl(it.iconPath)
            isProviderFound = true
            binding.bivNoPelanggan.clearError()
        }
    }

    fun normalizePhoneNumber(input: String): String {
        return input.replace(Regex("^((\\+62)|62|0)"), "")
    }

    protected fun checkContactPermission() {
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.READ_CONTACTS
            ) == PackageManager.PERMISSION_DENIED
        ) {
            ActivityCompat.requestPermissions(
                this, arrayOf(Manifest.permission.READ_CONTACTS), Constant.REQ_READ_CONTACT
            )
        } else {
            pickContact()
        }
    }

    private fun pickContact() {
        val intent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
        intent.type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE
        startActivityForResult(intent, Constant.REQ_READ_CONTACT)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == Constant.REQ_READ_CONTACT) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                pickContact()
            } else {
                GeneralHelper.showToast(
                    this, GeneralHelper.getString(R.string.access_not_granted)
                )
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_READ_CONTACT && resultCode == Activity.RESULT_OK) {
            val contactUri = data?.data ?: return
            val projection = arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER)

            val cursor = contentResolver.query(contactUri, projection, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val column = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                    val number = it.getString(column).replace("[^\\d.]".toRegex(), "")
                    var newNumber = number
                    if (number.substring(0, 1) != "0") {
                        newNumber = "0" + number.substring(2)
                    }
                    onContactPicked(newNumber)
                } else {
                    GeneralHelper.showToast(
                        this, GeneralHelper.getString(R.string.contact_not_found)
                    )
                }
            }
        }
    }

    protected open fun onContactPicked(number: String) {
        binding.bivNoPelanggan.setText(number.removeRange(0, 1))
        binding.btnSubmit.setEnabled(true)
        findProviderByPrefix(number)?.let {
            currentProvider = it
        }
    }

    private fun setupTabFunctionality() {
        // Set up tab click listeners
        if (binding.tabFavorit != null) {
            binding.tabFavorit.setOnClickListener { view -> switchToFavoritTab() }
        }

        if (binding.tabRiwayat != null) {
            binding.tabRiwayat.setOnClickListener { view -> switchToRiwayatTab() }
        }

        // Default to Favorit tab (without clearing search field on initial load)
        switchToFavoritTab()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponses,
                this,
                0,
                isFromFastMenu
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponses, this, 0, isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter
    }

    private fun switchToFavoritTab() {
        currentTab = TAB_FAVORIT
        updateTabAppearance()
        showFavoritContent()
    }

    private fun switchToRiwayatTab() {
        currentTab = TAB_RIWAYAT
        updateTabAppearance()
        showRiwayatContent()
    }

    private fun updateTabAppearance() {
        // Reset all tabs
        binding.tabFavorit.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        binding.tabRiwayat.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        // Set active tab
        when (currentTab) {
            TAB_FAVORIT -> {
                binding.tabFavorit.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }

            TAB_RIWAYAT -> {
                binding.tabRiwayat.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }
        }
    }

    private fun showFavoritContent() {
        binding.contentFavorit.visibility = View.VISIBLE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showRiwayatContent() {
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.VISIBLE
    }

    private fun updateEmptyStates() {
        // Update Favorit tab empty state
        if (savedResponses.isEmpty()) {
            binding.rvDaftarFavorit.visibility = View.GONE
            binding.llNoDataSaved.visibility = View.VISIBLE
        } else {
            binding.rvDaftarFavorit.visibility = View.VISIBLE
            binding.llNoDataSaved.visibility = View.GONE
        }

        // Update Riwayat tab empty state
        if (historyResponses.isEmpty()) {
            binding.rvRiwayat.visibility = View.GONE
            binding.llNoHistory.visibility = View.VISIBLE
        } else {
            binding.rvRiwayat.visibility = View.VISIBLE
            binding.llNoHistory.visibility = View.GONE
        }
    }

    override fun onClickSavedItem(savedResponse: SavedResponse?) {
        val number = savedResponse?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa.referenceNumber,
                if(isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
            ),savedResponses.toMutableList())
        }
    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            when(type) {
                Constant.EditOptionNs.FAV -> {
                    val savedId = savedResponseItem?.value!!.split("|")[0]

                    presenter.favoriteSavedList(param = SavedListNs(
                        savedId = savedId.toInt()
                    ))
                }
                Constant.EditOptionNs.EDIT -> {

                }
                Constant.EditOptionNs.HAPUS -> {
                    val bottomSheetFragment = HapusConfirmationBottomSheetFragment.newInstance(
                        savedResponseItem = savedResponseItem,
                        onConfirm = {
                            val savedId = savedResponseItem?.value!!.split("|")[0]

                            presenter.removeSavedList(param = SavedListNs(
                                savedId = savedId.toInt()
                            ))
                        },
                        onCancel = {
                            // Just dismiss the bottom sheet, no action needed
                        }
                    )
                    bottomSheetFragment.show(supportFragmentManager, "HapusConfirmationBottomSheet")
                }
                Constant.EditOptionNs.NON_FAV -> {
                    val savedId = savedResponseItem?.value!!.split("|")[0]

                    presenter.unfavoriteSavedList(param = SavedListNs(
                        savedId = savedId.toInt()
                    ))
                }
            }
        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        val number = historyResponse?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa.referenceNumber,
                if(isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
            ),savedResponses.toMutableList())
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val view = currentFocus

        if (ev?.action == MotionEvent.ACTION_DOWN && view is EditText) {
            val outRect = Rect()
            view.getGlobalVisibleRect(outRect)

            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)

            if (tappedOutsideEditText && tappedOutsideNumpad) {
                view.clearFocus()
                numpadHelper.hideKeyboard()
            }
        }

        return super.dispatchTouchEvent(ev)
    }

    fun findProviderByPrefix(phone: String): ProviderItem? {
        val cleanedPhone = phone.replace(Regex("[^0-9]"), "")
        val prefix4 = cleanedPhone.take(4)
        val prefix5 = if (cleanedPhone.length >= 5) cleanedPhone.take(5) else ""

        return providerList.firstOrNull { provider ->
            (prefix5.isNotEmpty() && GeneralHelper.isContains(provider.prefix, prefix5)) ||
                    GeneralHelper.isContains(provider.prefix, prefix4)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isFromFastMenu = false
    }
}
