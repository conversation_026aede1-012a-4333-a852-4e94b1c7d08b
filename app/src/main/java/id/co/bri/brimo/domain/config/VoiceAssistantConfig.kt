package id.co.bri.brimo.domain.config

object VoiceAssistantConfig {

    // Voice assistant response action
    const val CHECK_BALANCE = "balance"
    const val COMPLETE_TRANSFER = "complete_transfer"
    const val TRANSFER_CONFIRMATION = "transfer_confirmation"
    const val TRANSFER_METHODS = "transfer_methods"
    const val INPUT_PIN_TRANSFER = "input_pin"
    const val INPUT_PIN_BALANCE = "input_pin_balance"
    const val SAVED_LIST = "saved_list"
    const val ALL_RECIPIENT = "all_recipient"
    const val RECIPIENT_DETAIL = "recipient_detail"
    const val HOME_PAGE = "home_page"

    //Voice assistant request action
    const val CHAT = "chat"
    const val CHOOSE_RECIPIENT = "choose_recipient"
    const val CHOOSE_TRANSFER_METHOD = "choose_transfer_method"
    const val CHANGE_RECIPIENT = "change_recipient"
    const val EDIT_AMOUNT = "edit_amount"
    const val PROCESS_TRANSFER = "process_transfer"
    const val PROCESS_CHECK_BALANCE = "process_check_balance"
    const val PIN_VALIDATION = "pin_validation"

    //Voice assistant view type
    const val TEXT_USER_VIEW_TYPE = 0
    const val TEXT_ASSISTANT_VIEW_TYPE = 1
    const val BALANCE_VIEW_TYPE = 2
    const val LIST_RECIPIENT_VIEW_TYPE = 3
    const val LIST_TRANSFER_METHOD_VIEW_TYPE = 4
    const val CONFIRMATION_TRANSFER_VIEW_TYPE = 5
    const val RECEIPT_SUCCESS_VIEW_TYPE = 6
    const val RECEIPT_PENDING_VIEW_TYPE = 7
    const val LOADING_VIEW_TYPE = 8

    //Config for TTS
    const val PACKAGE_SPEECH_RECOGNIZER = "package:com.google.android.tts"
    const val TTS_ENGINE = "com.google.android.tts"
    const val TTS_VOICE = "id-id-x-idc-local"
    const val TTS_PITCH = 1.0f
    const val TTS_SPEECH_RATE = 1.0f
    const val ID_LANGUAGE = "id-ID"

    //Voice Assistant status
    const val VOICE_ASSISTANT_STATUS: String = "VoiceAssistant"
}