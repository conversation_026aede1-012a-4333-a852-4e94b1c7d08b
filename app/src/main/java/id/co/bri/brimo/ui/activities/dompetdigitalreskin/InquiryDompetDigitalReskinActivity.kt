package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dompetdigital.RekomendasiTopUpAdapterReskin
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IInquiryDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IInquiryDompetDigitalReskinView
import id.co.bri.brimo.databinding.ActivityInquiryDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.OptionAmountItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.customviews.switchbutton.SwitchView
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.attachNumpad
import java.math.BigInteger
import java.util.function.Consumer
import javax.inject.Inject

class InquiryDompetDigitalReskinActivity : NewSkinBaseActivity(), IInquiryDompetDigitalReskinView,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface,
    SumberDanaFragment.SelectSumberDanaInterface, SwitchView.ISwitchListener {

    private lateinit var binding: ActivityInquiryDompetDigitalReskinBinding
    private lateinit var mbrivaOpenResponse: List<BillingDetailOpen>
    private lateinit var openModel: BillingDetailOpen
    private var mListFailed: List<Int>? = mutableListOf()
    private var mListAccountModel: List<AccountModel>? = null
    private var model: AccountModel? = null
    private var saldo: Double = 0.0
    private var counter: Int = 0
    private var minTrx: Long = 0
    private var maxTrx: Long = 0
    private var saveStr: String = ""
    private var minTrxString: String = ""
    private var maxTrxString: String = ""
    private var feeAdminString: String = ""
    private var nominalStrClr: String = ""
    private var nominalString: String = ""

    private lateinit var adapter: RekomendasiTopUpAdapterReskin
    private lateinit var numpadHelper: CustomNumpadHelper

    @Inject
    lateinit var presenter: IInquiryDompetDigitalReskinPresenter<IInquiryDompetDigitalReskinView>

    companion object {

        private lateinit var mInquiryDompetRevampResponse: InquiryDompetDigitalResponse
        private var mUrlConfirm: String = ""
        private var mUrlPayment: String = ""
        private var mNominal = ""
        private var mPhoneNumber = ""
        private var isFromFastMenu: Boolean = false
        private var mSelectedWallet: EwalletProductResponse? = null
        private var mWalletBalance: EwalletBalanceResponse? = null

        private const val TAG_RESPONSE = "response"
        private const val TAG_PHONE_NUMBER = "tag_phone_number"
        private const val TAG_WALLET_INFO = "wallet_info"
        private const val TAG_WALLET_BALANCE = "wallet_balance"
        private const val NOTE_CHARACTER_LIMIT = 30

        @JvmStatic
        @JvmOverloads
        fun launchIntent(
            caller: Activity,
            inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
            urlConfirm: String,
            urlPayment: String,
            fromFastMenu: Boolean,
            nominal: String,
            phoneNumber: String,
            selectedWallet: EwalletProductResponse? = null,
            walletBalance: EwalletBalanceResponse? = null
        ) {
            val intent = Intent(caller, InquiryDompetDigitalReskinActivity::class.java)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(inquiryDompetDigitalResponse))
            intent.putExtra(TAG_WALLET_INFO, Gson().toJson(selectedWallet))
            intent.putExtra(TAG_PHONE_NUMBER, phoneNumber)
            if (walletBalance != null) {
                intent.putExtra(TAG_WALLET_BALANCE, Gson().toJson(walletBalance))
            }
            mUrlConfirm = urlConfirm
            mUrlPayment = urlPayment
            isFromFastMenu = fromFastMenu
            mNominal = nominal
            mPhoneNumber = phoneNumber
            mSelectedWallet = selectedWallet
            mWalletBalance = walletBalance
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryDompetDigitalReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (intent.extras != null) {
            parseIntent()
        }

        injectDependency()
        setupView()
    }

    private fun parseIntent() {
        if (intent.extras != null) {
            if (intent.hasExtra(TAG_RESPONSE)) {
                mInquiryDompetRevampResponse = Gson().fromJson(
                    intent.extras?.getString(TAG_RESPONSE),
                    InquiryDompetDigitalResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_WALLET_INFO)) {
                mSelectedWallet = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_INFO),
                    EwalletProductResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_PHONE_NUMBER)) {
                mPhoneNumber = intent?.extras?.getString(TAG_PHONE_NUMBER, "") ?: ""
            }

            if (intent.hasExtra(TAG_WALLET_BALANCE)) {
                mWalletBalance = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_BALANCE),
                    EwalletBalanceResponse::class.java
                )
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlConfirm(mUrlConfirm)
        presenter.getAccountList()
        presenter.start()
    }

//    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
//        val view = currentFocus
//
//        if (ev.action == MotionEvent.ACTION_DOWN && view is EditText) {
//            val outRect = Rect()
//            view.getGlobalVisibleRect(outRect)
//
//            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
//            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)
//
//            // ❗ hanya hide jika klik di luar EditText DAN di luar numpad
//            if (tappedOutsideEditText && tappedOutsideNumpad) {
//                view.clearFocus()
//                numpadHelper.hideKeyboard()
//            }
//        }
//
//        return super.dispatchTouchEvent(ev)
//    }

    private fun setupView() {

        GeneralHelperNewSkin.setToolbar(
            this, binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.ewallet)
        )

        val trimmedNumber = mPhoneNumber.replaceFirst(
            "^0+(?=\\d)".toRegex(),
            GeneralHelper.getString(R.string.hint_prefix_62)
        )

        // Create SpannableString to apply different colors to different parts
        val spannablePhoneNumber = SpannableString(trimmedNumber)
        val prefix = GeneralHelper.getString(R.string.hint_prefix_62)

        if (trimmedNumber.startsWith(prefix)) {
            // Apply disabled color to the "+62" prefix
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_disabled_default_ns)),
                0,
                prefix.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // Apply black color to the rest of the number
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_black_default_ns)),
                prefix.length,
                trimmedNumber.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.etNoPelanggan.setText(spannablePhoneNumber)
        binding.tvHint.setText(GeneralHelper.getString(R.string.txt_nomor_hp))

        binding.etNoPelanggan.isEnabled = false

        // Set nominal if hit inquiry from history
        if (mNominal != "") {
            // Set only the numeric value, prefixText will handle "Rp"
            val formattedNominal = GeneralHelper.formatNominalBiasa(mNominal.toDouble())

            // Find matching OptionAmountItem to get the properly formatted nominalString
            val matchingItem = mInquiryDompetRevampResponse.optionAmount.find {
                it.value.toString() == mNominal
            }

            // Use the formatted name from OptionAmountItem if found, otherwise create a fallback format
            nominalString = matchingItem?.name ?: "Rp${GeneralHelper.formatNominal(mNominal)}"

            binding.etNominal.setText(formattedNominal)
        } else {
            binding.etNominal.setText("")
        }

        binding.etNote.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            // Set character limit
            setFilters(arrayOf(InputFilter.LengthFilter(NOTE_CHARACTER_LIMIT)))
            // Initialize character counter
            updateNoteCounter()
            // Add text watcher for character counter
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    updateNoteCounter()
                }
            })
        }

        mbrivaOpenResponse = mInquiryDompetRevampResponse.billingDetailOpen
        feeAdminString = mInquiryDompetRevampResponse.adminFee.toString()

        for (billingDetailOpen in mbrivaOpenResponse)
            openModel = billingDetailOpen

        // Load wallet icon - use selected wallet info if available, otherwise use openModel
        if (mSelectedWallet != null) {
            // Load icon from selected connected wallet
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                mSelectedWallet!!.iconPath,
                mSelectedWallet!!.iconName?.split(".")?.get(0) ?: "",
                binding.ivWalletIcon,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital")
            )

            // Set wallet balance if available
            if (mWalletBalance != null) {
                binding.tvBalanceWallet.text = mWalletBalance!!.value_string ?: "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            } else {
                binding.tvBalanceWallet.text = "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            }
        } else {
            // Load icon from inquiry response
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                openModel.iconPath,
                openModel.iconName?.split("\\.")?.get(0) ?: "",
                binding.ivWalletIcon,
                0
            )

            // Hide balance for regular inquiry
            binding.tvBalanceWallet.visibility = View.GONE
        }

        binding.iconContainer.visibility = View.VISIBLE

        //set minimum
        if (mInquiryDompetRevampResponse.minimumTransactionString.isNotEmpty()) {
            minTrx = mInquiryDompetRevampResponse.minimumTransaction
            minTrxString = mInquiryDompetRevampResponse.minimumTransactionString
        }

        //set maximum
        if (mInquiryDompetRevampResponse.maximumTransactionString.isNotEmpty()) {
            maxTrx = mInquiryDompetRevampResponse.maximumTransaction
            maxTrxString = mInquiryDompetRevampResponse.maximumTransactionString
        }

        binding.helperText.setText(
            String.format(
                GeneralHelper.getString(R.string.minimal_rp),
                minTrxString
            )
        )

        saveStr = mInquiryDompetRevampResponse.nameDefault

        adapter = RekomendasiTopUpAdapterReskin(
            onItemClick = { selectedItem ->
                // Set only the numeric value, prefixText will handle "Rp"
                val formattedAmount =
                    GeneralHelper.formatNominalBiasa(selectedItem.value.toDouble())
                nominalString = selectedItem.name
                binding.etNominal.setText(formattedAmount)
                binding.etNominal.requestFocus()
                binding.etNominal.setSelection(binding.etNominal.text.length)
                checkButton()
            },
            items = mInquiryDompetRevampResponse.optionAmount
        )
        val layoutManager = GridLayoutManager(
            applicationContext, 2
        )
        binding.rvOptionAmount.layoutManager = layoutManager
        binding.rvOptionAmount.itemAnimator = DefaultItemAnimator()
        binding.rvOptionAmount.adapter = adapter

        initiateViews()
        setupSumberDanaView()

        // Trigger adapter selection and button check if nominal was set from history
        if (mNominal != "") {
            // Post to ensure text watchers are set up first
            binding.etNominal.post {
                updateAdapterSelection()
            }
        }

    }

    private fun setupSumberDanaView() {
        // Configure SumberDanaView properties
        binding.sdnView.isFastMenu = isFromFastMenu

        // Set click listeners for SumberDanaView
        binding.sdnView.setOnClickButton {
            // Safety check to ensure model is not null
            model?.let { accountModel ->
                ConfirmationDompetDigitalReskinActivity.launchIntent(
                    this,
                    openModel,
                    mInquiryDompetRevampResponse.saved,
                    mInquiryDompetRevampResponse.referenceNumber,
                    accountModel,
                    binding.etNote.getText(),
                    nominalStrClr,
                    nominalString,
                    mInquiryDompetRevampResponse.adminFee.toString(),
                    mInquiryDompetRevampResponse.adminFeeString,
                    mUrlConfirm,
                    mUrlPayment,
                    isFromFastMenu
                )
            }
        }

        binding.sdnView.setOnClickSumberDana {
            gantiSumberDana()
        }
    }

    private fun initiateViews() {
        // Create a unified text watcher that handles both formatting and selection
        val unifiedTextWatcher = object : TextWatcher {
            private var isUpdating = false

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                if (isUpdating) return

                isUpdating = true
                try {
                    // Format the numeric text with dots (e.g., 10000 -> 10.000)
                    val currentText = s.toString()
                    val cleanNumber = currentText.replace(".", "").replace(",", "").trim()

                    if (cleanNumber.isNotEmpty() && cleanNumber.all { it.isDigit() }) {
                        val formattedText = GeneralHelper.formatNominalBiasa(cleanNumber.toDouble())
                        if (currentText != formattedText) {
                            binding.etNominal.setText(formattedText)
                            binding.etNominal.setSelection(formattedText.length)
                        }
                    }

                    // Show/hide clear button based on content
                    binding.ivClearNominal.visibility =
                        if (currentText.isNotEmpty()) View.VISIBLE else View.GONE

                    // Update adapter selection and button state
                    updateAdapterSelection()
                } finally {
                    isUpdating = false
                }
            }
        }

        binding.etNominal.addTextChangedListener(unifiedTextWatcher)

        // Handle clear button click
        binding.ivClearNominal.setOnClickListener {
            binding.etNominal.setText("")
            binding.etNominal.requestFocus()
        }
    }

    override fun afterText(editable: Editable?) {
        updateAdapterSelection()
    }

    private fun updateAdapterSelection() {
        clearSelected()

        val normalizedInput = getNormalizedAmount()

        val index = mInquiryDompetRevampResponse.optionAmount.indexOfFirst {
            it.value.toString() == normalizedInput
        }

        mInquiryDompetRevampResponse.optionAmount.forEachIndexed { i, item ->
            item.setSelected(i == index)
        }

        // Update nominalString if a matching item is found
        if (index != -1) {
            nominalString = mInquiryDompetRevampResponse.optionAmount[index].name
        } else {
            // If no match found and input is not empty, create formatted string
            if (normalizedInput.isNotEmpty() && normalizedInput.all { it.isDigit() }) {
                nominalString = "Rp${GeneralHelper.formatNominal(normalizedInput)}"
            }
        }

        adapter.setSelectedPosition(index)
        adapter.notifyDataSetChanged()
        checkButton()
    }

    override fun onExceptionTrxExpired(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    private fun checkButton() {
        val rawNominal = binding.etNominal.text.toString()
        nominalStrClr = if (rawNominal.isEmpty()) "-"
        else rawNominal.replace(".", "").trim()

        val nominal = nominalStrClr.toLongOrNull()

        // Invalid input or empty
        if (rawNominal.isEmpty() || nominal == null || nominal == 0L) {
            disableButton(true)
            return
        }

        // Check if model is null (account data not loaded yet)
        if (model == null) {
            disableButton(true)
            return
        }

        val minRequired = BigInteger.valueOf(minTrx)
        val maxNominal = BigInteger.valueOf(maxTrx)
        val isMaxNominalLargerThanZero = maxNominal > BigInteger.ZERO
        val currentNominal = BigInteger.valueOf(nominal)
        val maxAllowed = BigInteger.valueOf(saldo.toLong() - getMinBalance(model!!))

        binding.helperText.apply {
            when {
                currentNominal < minRequired -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.text_minimal_top_up),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.red_ns_main
                        )
                    )
                }

                isMaxNominalLargerThanZero -> {
                    if (currentNominal > maxNominal) {
                        setText(
                            String.format(
                                GeneralHelper.getString(R.string.text_maximal_top_up),
                                maxTrxString
                            )
                        )
                        setTextColor(
                            ContextCompat.getColor(
                                this@InquiryDompetDigitalReskinActivity,
                                R.color.red_ns_main
                            )
                        )
                    } else {
                        setText(
                            String.format(
                                GeneralHelper.getString(R.string.minimal_rp),
                                minTrxString
                            )
                        )
                        setTextColor(
                            ContextCompat.getColor(
                                this@InquiryDompetDigitalReskinActivity,
                                R.color.text_gray_default_ns
                            )
                        )
                    }
                }

                else -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.minimal_rp),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.text_gray_default_ns
                        )
                    )
                }
            }
        }

        when {
            currentNominal < minRequired -> disableButton(true)
            currentNominal > maxNominal -> disableButton(isMaxNominalLargerThanZero)
            currentNominal > maxAllowed -> disableButton(!isFromFastMenu)
            else -> disableButton(false)
        }

        // Update SumberDanaView with payment amount
        binding.sdnView.payAmount = nominal?.toInt() ?: 0
    }

    private fun disableButton(disable: Boolean) {
            binding.sdnView.isEnableButton(!disable)
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        binding.sdnView.account = bankModel
        model = bankModel
        // Update saldo from the selected account
        saldo = bankModel.saldoReponse?.balance ?: 0.0
        checkButton()
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    private fun gantiSumberDana() {
        counter++
        if (mListAccountModel == null) {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list))
        } else {
            val selectedIndex = mListAccountModel?.indexOfFirst {
                model?.acoountString.equals(it.acoountString)
            }
            val rawNominal = binding.etNominal.text.toString()
            val nominalStr = if (rawNominal.isEmpty()) "0"
            else rawNominal.replace(".", "").trim()
            val nominalInt = nominalStr.toInt()

            val fragmentSumberDana =
                SumberDanaFragment(
                    mListAccountModel, this, counter, mListFailed, selectedIndex!!, nominalInt,
                    isFromFastMenu
                )
            fragmentSumberDana.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
        }
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mInquiryDompetRevampResponse.optionAmount.forEach(Consumer { p: OptionAmountItem ->
                p.isSelected = false
            })
        } else {
            for (p in mInquiryDompetRevampResponse.optionAmount) {
                p.isSelected = false
            }
        }
        adapter.notifyDataSetChanged()
    }

    private fun getNormalizedAmount(): String {
        val raw = binding.etNominal.text.toString()
        return raw.replace(".", "")
            .replace(",", "")
            .trim()
    }

    private fun getMinBalance(model: AccountModel): Long {
        val tempAccountModel =
            mListAccountModel?.firstOrNull { it.acoount == model.acoount } ?: AccountModel()
        return when {
            tempAccountModel.minimumBalance != null && tempAccountModel.minimumBalance >= 0 -> tempAccountModel.minimumBalance.toLong()
            model.minimumBalance != null && model.minimumBalance >= 0 -> model.minimumBalance.toLong()
            else -> 0L
        }
    }

    override fun onSwitchChange(isChecked: Boolean) {
        // Do nothing
    }

    private fun updateNoteCounter() {
        val currentLength = binding.etNote.getText().length
        binding.tvNoteCounter.text = "$currentLength/$NOTE_CHARACTER_LIMIT"
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>, mainAccount: AccountModel
    ) {
        model = mainAccount
        binding.sdnView.account = mainAccount
        mListAccountModel = accountList
        // Update saldo from the main account
        saldo = mainAccount.saldoReponse?.balance ?: 0.0
        // Trigger initial button check to set payAmount
        checkButton()
    }
}