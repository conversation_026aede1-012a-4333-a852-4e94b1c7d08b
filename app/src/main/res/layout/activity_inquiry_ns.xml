<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_confirmation_reskin"            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/sdn_view">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="0dp"
                android:layout_height="?attr/actionBarSize"
                android:background="@android:color/transparent"
                app:elevation="0dp"
                app:liftOnScroll="false"
                android:outlineProvider="bounds"
                app:layout_scrollFlags="scroll|enterAlways"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
                <TextView
                    android:id="@+id/textTitle"
                    android:text="Konfirmasi"
                    style="@style/SubTitleText.Bold.White"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:layout_gravity="center" />
            </androidx.appcompat.widget.Toolbar>

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@id/content"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@+id/toolbar"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:orientation="vertical">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appbarDashboard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    app:elevation="0dp"
                    app:liftOnScroll="false">

                    <com.google.android.material.appbar.CollapsingToolbarLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:minHeight="0dp"
                        app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                        app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                        app:scrimAnimationDuration="0"
                        app:titleEnabled="false">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@+id/toolbar"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="20dp">
                            <TextView
                                android:id="@+id/tv_capt_bill"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                android:text="Total Transaksi"
                                android:textSize="16sp"
                                android:textColor="@color/white" />
                            <TextView
                                android:id="@+id/totalTagihan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                app:layout_constraintTop_toBottomOf="@+id/tv_capt_bill"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                android:text="Rp502.000"
                                android:textSize="32sp"
                                android:textColor="@color/white" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.appbar.CollapsingToolbarLayout>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_card_rounded_ns"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="21dp"
                        android:paddingVertical="32dp">
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Tujuan transaksi"
                            android:textColor="@color/black"
                            android:textStyle="bold"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:id="@+id/textView6" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="21dp"
                            android:minHeight="85dp"
                            android:paddingHorizontal="21dp"
                            app:layout_constraintTop_toBottomOf="@+id/textView6"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:background="@drawable/bg_input_black_100_brimo_ns"
                            android:id="@+id/linearLayout6">

                            <RelativeLayout
                                android:layout_width="@dimen/space_x5"
                                android:layout_height="@dimen/space_x5">

                                <ImageView
                                    android:id="@+id/iv_area"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentTop="true"
                                    android:src="@drawable/ikon_wilayah" />
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                tools:ignore="RtlSymmetry"
                                android:layout_marginStart="@dimen/space_x2">

                                <TextView
                                    android:id="@+id/tv_name_cust"
                                    style="@style/BodySmallText.Medium.Grey"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/size_2dp"
                                    android:paddingVertical="@dimen/size_2dp"
                                    tools:text="Antonius"
                                    android:textColor="@color/black"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_number_cust"
                                    style="@style/BodySmallText.Medium.Grey"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/size_2dp"
                                    android:paddingVertical="@dimen/size_2dp"
                                    tools:text="PDAM - 000055812"
                                    android:textColor="@color/black" />

                            </LinearLayout>

                        </LinearLayout>

                        <RelativeLayout
                            android:id="@+id/rl_favorit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/background_cardview_noborder"
                            android:backgroundTint="#F5F7FB"
                            android:paddingHorizontal="21dp"
                            android:paddingVertical="@dimen/size_10dp"
                            android:layout_marginTop="21dp"
                            app:layout_constraintTop_toBottomOf="@+id/linearLayout6"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent">

                            <TextView
                                style="@style/Body3SmallText.Medium.NeutralDark40"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerVertical="true"
                                android:layout_toStartOf="@+id/switch_save"
                                android:end="@id/switch_pp"
                                android:text="Simpan ke Favorit" />

                            <androidx.appcompat.widget.SwitchCompat
                                android:id="@+id/switch_save"
                                android:layout_width="@dimen/space_x6_half"
                                android:layout_height="@dimen/space_x2_half"
                                app:thumbTint="@drawable/switch_thumb_color"
                                app:trackTint="@drawable/switch_track_color"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginBottom="@dimen/space_x2"
                                android:thumb="@drawable/tumb_selector"
                                android:track="@drawable/track_selector"
                                tools:ignore="UseSwitchCompatOrMaterialXml" />

                        </RelativeLayout>

<!--                        <com.google.android.material.textfield.TextInputLayout-->
<!--                            android:id="@+id/til_saved_name"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginTop="21dp"-->
<!--                            android:paddingTop="10dp"-->
<!--                            android:minHeight="64dp"-->
<!--                            app:hintTextColor="@color/text_black_default_ns"-->
<!--                            app:boxStrokeWidth="0dp"-->
<!--                            app:boxStrokeWidthFocused="0dp"-->
<!--                            android:background="@drawable/selector_input_field_ns"-->
<!--                            app:cursorColor="@color/text_black_default_ns"-->
<!--                            android:hint="Nama Tersimpan"-->
<!--                            android:visibility="gone"-->
<!--                            android:paddingHorizontal="21dp">-->

<!--                            <com.google.android.material.textfield.TextInputEditText-->
<!--                                android:id="@+id/tiet_saved_name"-->
<!--                                android:layout_width="match_parent"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:inputType="textCapCharacters"-->
<!--                                android:digits="@string/string_allowed_strip"-->
<!--                                android:background="@android:color/transparent"-->
<!--                                android:maxLines="1" />-->
<!--                        </com.google.android.material.textfield.TextInputLayout>-->

                        <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                            android:id="@+id/biv_save_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="21dp"
                            android:visibility="gone"
                            app:hintText="Nama Tersimpan" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Detail Tagihan"
                            android:textColor="@color/black"
                            android:textStyle="bold"
                            android:id="@+id/textView8"
                            app:layout_constraintTop_toBottomOf="@+id/rl_favorit"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:layout_marginTop="20dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingHorizontal="21dp"
                            android:paddingVertical="16dp"
                            app:layout_constraintTop_toBottomOf="@+id/textView8"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:background="@drawable/bg_input_black_100_brimo_ns"
                            android:layout_marginTop="21dp">

                            <LinearLayout
                                android:id="@+id/ll_more_content"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:animateLayoutChanges="true"
                                android:visibility="gone" />

                            <View
                                android:id="@+id/v_line_bill_detail"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#E9EEF6"
                                android:layout_marginVertical="8dp"
                                android:visibility="gone"/>

                            <!-- Nominal Row -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="8dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Nominal"
                                    android:textColor="#9CA3AF"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_nominal"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp500.000"
                                    android:textColor="#000000"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- Biaya Admin Row -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="8dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Biaya Admin"
                                    android:textColor="#9CA3AF"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_admin_fee"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp2.000"
                                    android:textColor="#000000"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- Lihat Lebih -->
                            <TextView
                                android:id="@+id/btnLihatLebih"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lihat Lebih "
                                android:drawableEnd="@drawable/ic_arrow_down_blue"
                                android:textColor="#2563EB"
                                android:textStyle="bold"
                                android:textSize="14sp"
                                android:gravity="center"
                                android:layout_gravity="center_horizontal" />
                        </LinearLayout>
                    </LinearLayout>

                </androidx.core.widget.NestedScrollView>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <id.co.bri.brimo.ui.widget.sumberdana.SumberDanaView
            android:id="@+id/sdn_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:buttonType="closedBill"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

<!--        <View-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="@dimen/size_1dp"-->
<!--            android:background="#E9EEF6"-->
<!--            app:layout_constraintBottom_toTopOf="@+id/ly_total_detail" />-->

<!--        <LinearLayout-->
<!--            android:id="@+id/ly_total_detail"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_alignParentBottom="true"-->
<!--            android:background="@color/whiteColor"-->
<!--            android:orientation="vertical"-->
<!--            android:visibility="visible"-->
<!--            android:padding="16dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent">-->

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal"-->
<!--                android:paddingVertical="16dp"-->
<!--                android:gravity="center_vertical"-->
<!--                android:background="@android:color/white">-->

<!--                &lt;!&ndash; Card Image &ndash;&gt;-->
<!--                <ImageView-->
<!--                    android:id="@+id/iv_rekening"-->
<!--                    android:layout_width="48dp"-->
<!--                    android:layout_height="32dp"-->
<!--                    android:src="@drawable/ic_card_britama"-->
<!--                    android:scaleType="fitCenter"-->
<!--                    android:layout_marginEnd="12dp" />-->

<!--                &lt;!&ndash; Text Container &ndash;&gt;-->
<!--                <LinearLayout-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical">-->

<!--                    &lt;!&ndash; Account Info &ndash;&gt;-->
<!--                    <TextView-->
<!--                        android:id="@+id/tv_number_account"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        tools:text="0290 3445 9681 112 - @agung_hars"-->
<!--                        android:textSize="12sp"-->
<!--                        android:textColor="#000000" />-->

<!--                    &lt;!&ndash; Balance &ndash;&gt;-->
<!--                    <TextView-->
<!--                        android:id="@+id/tv_nominal_account"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="-"-->
<!--                        android:textSize="16sp"-->
<!--                        android:textStyle="bold"-->
<!--                        android:textColor="#000000"-->
<!--                        android:layout_marginTop="4dp" />-->
<!--                </LinearLayout>-->

<!--                &lt;!&ndash; Edit (Ubah) Button &ndash;&gt;-->
<!--                <TextView-->
<!--                    android:id="@+id/btnUbah"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="Ubah"-->
<!--                    android:textColor="#2563EB"-->
<!--                    android:textStyle="bold"-->
<!--                    android:textSize="14sp"-->
<!--                    android:visibility="gone" />-->

<!--                <ImageView-->
<!--                    android:id="@+id/iv_change"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:src="@drawable/ic_chevron_down_reskin" />-->
<!--            </LinearLayout>-->

<!--            <id.co.bri.brimo.ui.widget.BayarButtonView-->
<!--                android:id="@+id/btnSubmit"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                app:labelText="Bayar Sekarang"-->
<!--                tools:amountText="Rp500.000"/>-->

<!--        </LinearLayout>-->
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
