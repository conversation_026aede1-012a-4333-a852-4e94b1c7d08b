<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@drawable/bg_new_skin_activity_container"
    tools:context=".ui.activities.inforekeningnewskin.EditAliasNameActivity">

  <include
      android:id="@+id/toolbar"
      layout="@layout/toolbar_new_skin" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:background="@color/transparent"/>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

  <androidx.coordinatorlayout.widget.CoordinatorLayout
      android:id="@+id/contents"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_below="@id/toolbar"
      android:background="@drawable/bg_new_skin_activity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

          <com.google.android.material.textfield.TextInputLayout
              android:id="@+id/input_alias"
              android:layout_width="match_parent"
              android:layout_height="@dimen/space_x8"
              android:layout_marginTop="@dimen/size_24dp"
              android:background="@drawable/selector_input_field_ns"
              android:gravity="center_vertical"
              android:hint="@string/alias_name"
              android:minHeight="@dimen/size_64dp"
              android:paddingHorizontal="@dimen/size_8dp"
              android:textColorHint="@color/black_ns_main"
              app:boxStrokeWidth="0dp"
              app:boxStrokeWidthFocused="0dp"
              app:endIconMode="clear_text"
              app:endIconDrawable="@drawable/ic_clear_input_ns"
              app:hintTextAppearance="@style/TextHint.Small"
              app:hintTextColor="@color/ns_hint_color">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_nama_alias"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:maxLength="16"
                android:maxLines="1"
                tools:singleLine="true"
                android:background="@android:color/transparent"
                android:inputType="text"
                android:textSize="14sp"
                android:textColor="@color/text_black_default_ns" />
          </com.google.android.material.textfield.TextInputLayout>

          <TextView
              android:id="@+id/tv_alias_desc"
              style="@style/Body3SmallText.Regular.NeutralLight60"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_marginTop="@dimen/space_half"
              android:textColor="@color/black_ns_600"
              android:text="@string/minimum_five_char" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/ns_black100"
            app:layout_constraintBottom_toTopOf="@id/layout_button"/>

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/space_x2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:orientation="horizontal">

          <androidx.appcompat.widget.AppCompatButton
              android:id="@+id/btn_save"
              style="@style/ButtonPrimaryNewSkin"
              android:layout_width="0dp"
              android:layout_height="@dimen/space_x6"
              android:layout_weight="1"
              android:enabled="false"
              android:text="@string/simpan"
              android:textAllCaps="false" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

  </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>