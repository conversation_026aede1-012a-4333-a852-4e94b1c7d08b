<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools">

	<androidx.constraintlayout.widget.ConstraintLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:fitsSystemWindows="true">

		<androidx.constraintlayout.widget.ConstraintLayout
				android:layout_width="0dp"
				android:layout_height="0dp"
				app:layout_constraintTop_toTopOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintBottom_toTopOf="@+id/sdn_view">
			<!-- Toolbar -->
			<include
					android:id="@+id/toolbar"
					layout="@layout/toolbar_brimo_ns"
					app:layout_constraintTop_toTopOf="parent"
					app:layout_constraintLeft_toLeftOf="parent"
					app:layout_constraintRight_toRightOf="parent" />

			<!-- Scrollable content -->
			<ScrollView
					android:layout_width="0dp"
					android:layout_height="0dp"
					app:layout_constraintTop_toTopOf="parent"
					app:layout_constraintLeft_toLeftOf="parent"
					app:layout_constraintRight_toRightOf="parent"
					app:layout_constraintBottom_toBottomOf="parent"
					android:layout_marginTop="70dp"
					android:paddingHorizontal="16dp"
					android:background="@drawable/bg_card_rounded_ns"
					android:clipToPadding="false">

				<LinearLayout
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical"
						android:paddingTop="16dp">

					<LinearLayout
							android:id="@+id/ll_wallet_selection"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="horizontal"
							android:paddingHorizontal="16dp"
							android:gravity="center_vertical|center_horizontal"
							android:layout_gravity="center_horizontal"
							android:minHeight="64dp"
							android:background="@drawable/bg_input_black_100_brimo_ns">

						<RelativeLayout
								android:id="@+id/icon_container"
								android:layout_width="@dimen/size_32dp"
								android:layout_height="@dimen/size_32dp"
								android:layout_gravity="center_vertical"
								android:background="@drawable/round_icon_ns"
								android:visibility="gone"
								tools:visibility="visible">

							<ImageView
									android:id="@+id/iv_wallet_icon"
									android:layout_width="match_parent"
									android:layout_height="match_parent"
									android:layout_alignParentTop="true"
									android:src="@drawable/ikon_wilayah" />
						</RelativeLayout>

						<LinearLayout
								android:layout_width="0dp"
								android:layout_height="wrap_content"
								android:layout_weight="1"
								android:orientation="vertical"
								tools:ignore="RtlSymmetry">

							<TextView
									android:id="@+id/tv_hint"
									style="@style/BodySmallText.Medium.Grey"
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									android:textColor="@color/text_black_default_ns"
									android:paddingLeft="16dp"
									android:textSize="@dimen/micro_text"
									tools:text="GoPay"
									android:visibility="visible" />


							<com.google.android.material.textfield.TextInputLayout
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									app:hintEnabled="false"
									android:layout_marginTop="@dimen/size_4dp"
									android:gravity="center_vertical"
									app:boxStrokeWidth="0dp">

								<EditText
										android:id="@+id/et_no_pelanggan"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										tools:hint="08214136001"
										android:focusable="false"
										android:textColorHint="@color/text_black_default_ns"
										android:layout_gravity="center_vertical"
										android:background="@android:color/transparent"
										android:textStyle="bold"
										android:paddingRight="10dp"
										android:inputType="none|textNoSuggestions"
										android:paddingVertical="0dp" />
							</com.google.android.material.textfield.TextInputLayout>

					</LinearLayout>
							<TextView
									android:id="@+id/tv_balance_wallet"
									android:layout_width="wrap_content"
									android:layout_height="wrap_content"
									android:textStyle="bold"
									android:textAlignment="textEnd"
									android:textColor="@color/black_ns_main"
									tools:text="Rp8.000.000.000.000"/>
						</LinearLayout>

					<TextView
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:text="Input Nominal"
							android:layout_marginTop="24dp"
							android:textSize="14sp"
							android:textStyle="bold"
							android:textColor="@color/black_ns_main" />

					<LinearLayout
							android:id="@+id/et_nominal_container"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:minHeight="80dp"
							android:gravity="center_vertical"
							android:orientation="vertical"
							android:paddingVertical="12dp"
							android:layout_marginTop="@dimen/space_x2"
							android:layout_marginBottom="@dimen/space_half"
							android:background="@drawable/selector_input_field_ns">

						<TextView
								android:id="@+id/tv_label_nominal"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:layout_marginStart="16dp"
								android:layout_marginBottom="4dp"
								android:textColor="@color/black_ns_main"
								android:text="@string/nominal" />

						<!-- Custom layout with always-visible "Rp" prefix -->
						<LinearLayout
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:orientation="horizontal"
								android:gravity="center_vertical"
								android:paddingHorizontal="16dp"
								android:background="@drawable/selector_input_field_ns">

							<TextView
									android:id="@+id/tv_rp_prefix"
									android:layout_width="wrap_content"
									android:layout_height="wrap_content"
									android:text="@string/rp"
									android:textSize="@dimen/size_24sp"
									android:textStyle="bold"
									android:textColor="@color/black_ns_main"
									android:layout_marginEnd="4dp" />

							<EditText
									android:id="@+id/et_nominal"
									android:layout_width="0dp"
									android:layout_height="wrap_content"
									android:layout_weight="1"
									android:textSize="@dimen/size_24sp"
									android:paddingBottom="0dp"
									android:textStyle="bold"
									android:inputType="number"
									android:maxLength="15"
									android:maxLines="1"
									android:background="@android:color/transparent"
									android:textColorHint="@color/text_gray_default_ns"
									android:textColor="@color/black_ns_main" />

							<ImageView
									android:id="@+id/iv_clear_nominal"
									android:layout_width="24dp"
									android:layout_height="24dp"
									android:src="@drawable/ic_clear_ns"
									android:background="?attr/selectableItemBackgroundBorderless"
									android:padding="4dp"
									android:visibility="gone" />

						</LinearLayout>
					</LinearLayout>

					<TextView
							android:id="@+id/helper_text"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:layout_marginTop="8dp"
							tools:text="@string/minimal_rp"
							android:textSize="14sp"
							android:textColor="@color/text_gray_default_ns" />

					<id.co.bri.brimo.ui.widget.input_til.BaseInputView
							android:id="@+id/et_note"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:layout_marginTop="8dp"
							android:textColor="@color/black_ns_main"
							app:hintText="Catatan (Optional)" />
					
					<TextView
							android:id="@+id/tv_note_counter"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:layout_marginTop="@dimen/size_8dp"
							android:layout_gravity="end"
							tools:text="0/30"
							android:textSize="12sp"
							android:textColor="@color/text_gray_default_ns" />

					<TextView
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:layout_marginTop="24dp"
							android:text="@string/pilih_nominal_donasi"
							android:textStyle="bold"
							android:textColor="@color/black_ns_main" />

					<androidx.recyclerview.widget.RecyclerView
							android:id="@+id/rv_option_amount"
							android:layout_width="match_parent"
							android:layout_height="0dp"
							android:layout_marginTop="16dp"
							android:layout_weight="1"
							android:fillViewport="true"
							tools:itemCount="1"
							tools:listitem="@layout/item_nominal_reskin" />

				</LinearLayout>
			</ScrollView>
		</androidx.constraintlayout.widget.ConstraintLayout>

		<id.co.bri.brimo.ui.widget.sumberdana.SumberDanaView
			android:id="@+id/sdn_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			app:buttonType="openBill"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintBottom_toBottomOf="parent" />

	</androidx.constraintlayout.widget.ConstraintLayout>
</layout>